{"name": "dtbx-backoffice", "private": true, "type": "module", "scripts": {"build": "turbo build", "dev": "turbo dev", "build-client": "turbo build-client", "dev-client": "turbo dev-client", "dev:landing": "turbo dev --filter=landing...", "dev:lms": "turbo dev --filter=lms...", "dev:x247": "turbo dev --filter=x247...", "dev:eatta": "turbo dev --filter=eatta...", "dev:ams": "turbo dev --filter=ams...", "dev:nms": "turbo dev --filter=nms...", "dev:cms": "turbo dev --filter=cms...", "dev-client:eatta": "turbo dev-client --filter=eatta...", "build:landing": "turbo build --filter=landing...", "build:lms": "turbo build --filter=lms...", "build:x247": "turbo build --filter=x247...", "build:eatta": "turbo build --filter=eatta...", "build:ams": "turbo build --filter=ams...", "build:nms": "turbo build --filter=nms...", "build:cms": "turbo build --filter=cms...", "build:boma": "turbo build --filter=boma-yangu...", "build-client:eatta": "turbo build-client --filter=eatta...", "lint": "turbo lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "clean": "turbo clean && rm -rf .turbo", "test": "turbo run test", "test:watch": "turbo run test:watch", "test:coverage": "turbo run view-report", "e2e:dev": "turbo cy:open --ui=stream", "test:e2e": "turbo test:e2e --ui=stream", "e2e:coverage": "turbo e2e:coverage"}, "devDependencies": {"prettier": "^3.5.3", "turbo": "^2.4.4", "typescript": "^5.8.2", "vitest": "^3.0.9"}, "packageManager": "pnpm@10.11.0", "engines": {"node": ">=20"}, "pnpm": {"overrides": {"vite": "6.2.3", "@babel/helpers": "^7.26.10", "brace-expansion": "4.0.1"}, "onlyBuiltDependencies": ["esbuild"]}}