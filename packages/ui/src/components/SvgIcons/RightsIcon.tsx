import path from 'path'
import { ComponentProps } from 'react'

export interface IRightsIconProps extends ComponentProps<'svg'> {
  width?: string
  height?: string
  fill?: string
  stroke?: string
  strokeWidth?: string
}

export const RightsIcon = ({
  width = '21',
  height = '21',
  fill = 'none',
  stroke = '#2A3339',
  strokeWidth = '1.66667',
  ...rest
}: IRightsIconProps) => {
  return (
    <svg
      width={width}
      height={height}
      viewBox="0 0 21 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6.13509 9.66667V7.16667C6.13509 4.86548 8.00057 3 10.3018 3C12.3176 3 13.999 4.43147 14.3851 6.33333M6.80176 18H13.8018C15.2019 18 15.902 18 16.4367 17.7275C16.9071 17.4878 17.2896 17.1054 17.5293 16.635C17.8018 16.1002 17.8018 15.4001 17.8018 14V13.6667C17.8018 12.2665 17.8018 11.5665 17.5293 11.0317C17.2896 10.5613 16.9071 10.1788 16.4367 9.93915C15.902 9.66667 15.2019 9.66667 13.8018 9.66667H6.80176C5.40163 9.66667 4.70156 9.66667 4.16678 9.93915C3.69638 10.1788 3.31392 10.5613 3.07424 11.0317C2.80176 11.5665 2.80176 12.2665 2.80176 13.6667V14C2.80176 15.4001 2.80176 16.1002 3.07424 16.635C3.31392 17.1054 3.69638 17.4878 4.16678 17.7275C4.70156 18 5.40163 18 6.80176 18Z"
        stroke={stroke}
        strokeWidth={strokeWidth}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  )
}
