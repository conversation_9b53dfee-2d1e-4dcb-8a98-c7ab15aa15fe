'use client'
import {
  Box,
  IconButton,
  Link,
  List,
  ListItemButton,
  ListItemIcon,
  ListItemText,
  Stack,
  styled,
  Tooltip,
} from '@mui/material'

import { ReactNode, useEffect, useState } from 'react'
import { usePathname, useSearchParams } from 'next/navigation'

import {
  InternalCollapsedLogo,
  InternalNavLogo,
  NavMenuIcon,
} from '../SvgIcons'
import { useCustomRouter } from '../../hooks'
import { ISidebarConfigItem } from './SidebarConfig'
import { isNavMatch } from '../../utils/helpers'

const ListItemStyle = styled(ListItemButton)(() => ({
  height: 48,
  position: 'relative',
  display: 'flex',
  flexDirection: 'row',
  flexWrap: 'nowrap',
  textTransform: 'capitalize',
  color: '#2A3339',
  '&:hover': {
    backgroundColor: '#F3F4F5',
    color: '#2A3339',
  },
}))
const NavBarStyle = styled(Box)(() => ({
  backgroundColor: '#FAFAFA',
  paddingTop: '1.5%',
  minHeight: `100vh`,
  borderRight: '1px solid #E7E8E9',
  display: 'flex',
  flexDirection: 'column',
  alignItems: 'flex-start',
  justifyContent: 'space-between',
  gap: '10px',
}))

const ListItemIconStyle = styled(ListItemIcon)({
  width: 22,
  height: 22,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
})

function NavItem({
  item,
  active,
  collapsed,
}: {
  item: ISidebarConfigItem
  active: (path: string) => boolean
  collapsed: boolean
}) {
  const { title, path, icon } = item
  const isActiveRoot = active(path)

  const activeRootStyle = {
    color: '#000A12',
    fontWeight: 'fontWeightMedium',
    backgroundColor: '#E7E8E9',
    '&:before': { display: 'flex' },
    '& $title': {
      fontWeight: 400,
    },
    '& $icon': {
      color: '#000A12',
    },
    borderRadius: '4px',
  }
  const router = useCustomRouter()
  return (
    <Link onClick={() => router.push(path)} underline={'none'}>
      <ListItemStyle
        disableGutters
        sx={{
          ...(isActiveRoot && activeRootStyle),
        }}
      >
        <Tooltip title={collapsed ? title : ''} placement={'right'}>
          <ListItemIconStyle>{icon && icon}</ListItemIconStyle>
        </Tooltip>
        {!collapsed && (
          <ListItemText
            disableTypography
            primary={title}
            sx={{
              minWidth: '150px',
            }}
          />
        )}
      </ListItemStyle>
    </Link>
  )
}

type SidebarProps = {
  sidebarConfig: ISidebarConfigItem[]
  sidebarCollapsed: (open: boolean) => void
  bgColor?: string
  linkHref?: string
  footer?: ReactNode
}
export const Sidebar = ({
  sidebarConfig,
  sidebarCollapsed,
  bgColor = '#FAFAFA',
  linkHref = '/landing',
  footer,
}: SidebarProps) => {
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const isProductionEnv = process.env.NEXT_PUBLIC_ENVIRONMENT === 'prod'
  const isUATEnv = process.env.NEXT_PUBLIC_ENVIRONMENT === 'uat'
  const handleCollapse = () => {
    setCollapsed(!collapsed)
    sidebarCollapsed(!collapsed)
  }
  const [collapsed, setCollapsed] = useState(false)
  const router = useCustomRouter()
  const handleResize = () => {
    const shouldCollapse = window.innerWidth < 1300
    setCollapsed(shouldCollapse)
    sidebarCollapsed(shouldCollapse)
  }
  useEffect(() => {
    // Ensure the window object is only accessed in the client
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', handleResize)
      handleResize()

      return () => window.removeEventListener('resize', handleResize)
    }
  }, [])
  useEffect(() => {
    if (pathname === '/reports') {
      handleCollapse()
    }
  }, [pathname])

  return (
    <NavBarStyle
      sx={{
        backgroundColor: bgColor,
        minWidth: collapsed ? '5vw' : '14rem',
        px: collapsed ? '1%' : '2%',
        transition: 'min-width 0.3s, padding 0.3s',
      }}
    >
      <Stack alignItems="start">
        <IconButton
          href={linkHref}
          sx={{
            '&:hover': {
              backgroundColor: 'transparent',
            },
            padding: '0',
            paddingLeft: '3.5%',
            transition: 'all 0.3s',
          }}
        >
          {' '}
          <Box
            sx={{
              width: collapsed ? '44px' : '123px',
              transition: 'width 0.3s',
            }}
          >
            {collapsed ? <InternalCollapsedLogo /> : <InternalNavLogo />}
          </Box>
        </IconButton>
        <Tooltip
          title={collapsed ? 'Expand Menu' : 'Collapse Menu'}
          placement={'right'}
        >
          <IconButton
            onClick={handleCollapse}
            sx={{
              '&:hover': {
                backgroundColor: 'transparent',
              },
            }}
          >
            <NavMenuIcon />
          </IconButton>
        </Tooltip>

        <Box>
          <List disablePadding>
            {sidebarConfig.map((item) => {
              // In the dev environment, we assume everything is ready unless explicitly disabled
              if (
                (isProductionEnv && item.isProductionReady) || // Only show if production is ready
                (isUATEnv && item.isUATReady !== false) || // Show if UAT is not explicitly disabled
                (!isProductionEnv && !isUATEnv) // In dev, show everything by default
              ) {
                return (
                  <NavItem
                    key={item.title}
                    item={item}
                    active={() => isNavMatch(item.path, pathname, searchParams)}
                    collapsed={collapsed}
                  />
                )
              }
              return null // Don't render the item if not ready for the environment
            })}
          </List>
        </Box>
      </Stack>

      {footer && footer}
    </NavBarStyle>
  )
}
