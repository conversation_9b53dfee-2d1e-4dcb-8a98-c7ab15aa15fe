'use client'
import { useState } from 'react'
import {
  <PERSON>er,
  <PERSON><PERSON>,
  <PERSON>ton,
  DialogTitle,
  IconButton,
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Paper,
} from '@mui/material'
import CloseIcon from '@mui/icons-material/Close'
import { IHeadCell } from '@dtbx/store/interfaces'
import { CustomSkeleton } from '@dtbx/ui/components'
import { CustomTableHeader } from '@dtbx/ui/components/Table'
import { CustomSearchInput } from '@dtbx/ui/components/Input'
import { SearchRounded } from '@mui/icons-material'
import { useAppSelector } from '@/store'
import { formatTimestamp } from '@dtbx/store/utils'
import { sentenceCase } from 'tiny-case'

export const CardsChangesLogDrawer = () => {
  const [open, setOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const { branchApprovalRequests } = useAppSelector(
    (state) => state.approvals
  )
  const header: IHeadCell[] = [
    {
      id: 'event',
      label: 'Event Type',
      alignCenter: false,
      alignRight: false,
    },
    {
      id: 'status',
      label: 'Status',
      alignCenter: false,
      alignRight: false,
    },
    {
      id: 'maker',
      label: 'Maker',
      alignCenter: false,
      alignRight: false,
    },
    {
      id: 'makerTimestamp',
      label: 'Maker Timestamp',
      alignCenter: false,
      alignRight: false,
    },
    {
      id: 'checker',
      label: 'Checker',
      alignCenter: false,
      alignRight: false,
    },
    {
      id: 'checkerTimestamp',
      label: 'Checker Timestamp',
      alignCenter: false,
      alignRight: false,
    },
    {
      id: 'actions',
      label: 'Actions',
      alignCenter: false,
      alignRight: false,
    },
  ]

  // Filter approval requests based on search term
  const filteredApprovals = branchApprovalRequests?.filter((approval) =>
    approval.makerCheckerType.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    approval.maker.toLowerCase().includes(searchTerm.toLowerCase()) ||
    approval.status.toLowerCase().includes(searchTerm.toLowerCase()) ||
    (approval.checker && approval.checker.toLowerCase().includes(searchTerm.toLowerCase()))
  ) || []

  const handleClose = (_e: React.SyntheticEvent, reason: string) => {
    if (reason === 'backdropClick') {
      return
    }
    setOpen(false)
  }

  const isLoading = false

  return (
    <>
      {' '}
      <Button
        variant="outlined"
        onClick={() => setOpen(!open)}
        sx={{
          textWrap: 'noWrap',
          border: '1px solid #D0D5DD',
          borderRadius: '4px',
          boxShadow: '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
          height: '2.5rem',
          color: '#555C61',
          fontSize: '15px',
          fontWeight: '500',
          display: branchApprovalRequests && branchApprovalRequests.length > 0 ? 'flex' : 'none',
        }}
      >
        Changes Log
      </Button>
      <Drawer
        sx={{
          '.MuiDrawer-paper': {
            width: '95%',
          },
        }}
        open={open}
        anchor={'right'}
        onClose={handleClose}
      >
        {' '}
        <Box
          sx={{
            background: '#F9FAFB',
            borderBottom: '2px solid  #F2F4F7',
            height: '3.5rem',
            display: 'flex',
          }}
        >
          <Stack
            flexDirection="row"
            sx={{
              alignItems: 'center',
              px: '2%',
            }}
          >
            <DialogTitle
              sx={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
                alignContent: 'center',
                py: '5px',
              }}
            >
              <Typography variant="subtitle2" color={'primary.main'}>
                Changes Log
              </Typography>
            </DialogTitle>
          </Stack>
          <IconButton
            aria-label="close"
            onClick={(e) => handleClose(e, 'close')}
            sx={{
              position: 'absolute',
              right: 8,
              top: 8,
              color: '',
            }}
          >
            <CloseIcon />
          </IconButton>
        </Box>
        <Stack spacing={2} padding={{ xs: 1.5, sm: 1.5, md: '1.5rem 2.5rem' }}>
          <CustomSearchInput
            sx={{
              width: '30%',
              '&.Mui-focused': {
                width: '40%',
              },
              borderRadius: '4px',
              '& fieldset': {
                border: '1px solid #D0D5DD !important',
              },
            }}
            startAdornment={<SearchRounded />}
            placeholder="Search by event type, maker, status..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <Stack>
            <Paper
              elevation={0}
              sx={{
                border: '1px solid #EAECF0',
                boxShadow:
                  '0px 1px 3px 0px rgba(16, 24, 40, 0.10), 0px 1px 2px 0px rgba(16, 24, 40, 0.06)',
              }}
            >
              <TableContainer
                component={Paper}
                elevation={0}
                sx={{
                  boxShadow: 'none',
                }}
              >
                {!isLoading ? (
                  filteredApprovals?.length > 0 ? (
                    <Table
                      sx={{ minWidth: 650 }}
                      aria-label="approval requests table"
                    >
                      <CustomTableHeader
                        order={'desc'}
                        orderBy={''}
                        rowCount={0}
                        headLabel={[...header]}
                        numSelected={0}
                      />
                      <TableBody>
                        {filteredApprovals.map((approval) => {
                          return (
                            <TableRow key={approval.id} hover>
                              <TableCell>{approval.makerCheckerType.name}</TableCell>
                              <TableCell>
                                <Typography
                                  sx={{
                                    color: approval.status === 'PENDING' ? '#F79009' :
                                           approval.status === 'APPROVED' ? '#12B76A' :
                                           approval.status === 'REJECTED' ? '#F04438' : '#667085',
                                    fontWeight: 500,
                                  }}
                                >
                                  {sentenceCase(approval.status)}
                                </Typography>
                              </TableCell>
                              <TableCell>{approval.maker}</TableCell>
                              <TableCell>{formatTimestamp(approval.dateCreated)}</TableCell>
                              <TableCell>{approval.checker || 'N/A'}</TableCell>
                              <TableCell>
                                {approval.dateModified !== approval.dateCreated
                                  ? formatTimestamp(approval.dateModified)
                                  : 'N/A'}
                              </TableCell>
                              <TableCell>
                                <Button
                                  variant="outlined"
                                  sx={{
                                    border: '1px solid #D0D5DD',
                                    height: '2.5rem',
                                    boxShadow:
                                      '0px 1px 2px 0px rgba(16, 24, 40, 0.05)',
                                    color: '#555C61',
                                    fontSize: '15px',
                                    fontWeight: '500',
                                  }}
                                  onClick={() => {
                                    // Handle view action - could open approval details
                                    console.log('View approval:', approval)
                                  }}
                                >
                                  View
                                </Button>
                              </TableCell>
                            </TableRow>
                          )
                        })}
                      </TableBody>
                    </Table>
                  ) : (
                    <Box sx={{ p: 4, textAlign: 'center' }}>
                      <Typography variant="h6" color="text.secondary">
                        No approval requests found
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {searchTerm ? 'Try adjusting your search criteria' : 'No approval history available for this card'}
                      </Typography>
                    </Box>
                  )
                ) : (
                  <CustomSkeleton
                    variant="rectangular"
                    animation="wave"
                    sx={{
                      height: '70vh',
                      width: '100%',
                    }}
                  />
                )}
              </TableContainer>
            </Paper>
          </Stack>
        </Stack>
      </Drawer>
    </>
  )
}
