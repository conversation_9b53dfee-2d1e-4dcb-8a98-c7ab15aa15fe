'use client'
import React, { useState } from 'react'
import {
  Stack,
  Typography,
  Avatar,
  Grid,
  InputAdornment,
  IconButton,
} from '@mui/material'
import { CardHeaderIcon } from '@dtbx/ui/components/SvgIcons'
import { CustomActiveChip, CustomErrorChip } from '@dtbx/ui/components/Chip'
import { ReadOnlyTypography } from '@dtbx/ui/components'
import { useAppSelector } from '@/store'
import { ActivateCreditCard } from '@/app/credit-cards/c-card/Activate'
import { formatDateTime, formatTimestamp } from '@dtbx/store/utils'
import { ResetPinRetriesCard } from '@/app/credit-cards/c-card/ResetPinRetries'
import { Visibility, VisibilityOff } from '@mui/icons-material'
import { getPan } from '@/store/actions'
import { useAppDispatch } from '@dtbx/store'
import { clearKeys, decryptData, getPublicKey } from '../../../utils/SecretKeysOperations'
import { setIsLoadingPan } from '@/store/reducers'

export const CardView = () => {
  const { selectedCardToView } = useAppSelector((state) => state.cards)
  const [showPan, setShowPan] = useState(false)
  const [pan, setPan] = useState('')

  const dispatch = useAppDispatch()
  const isLoadingPan = useAppSelector((state) => state.cards.isLoadingPan)

  type FieldConfig = {
    key: string
    label: string
    value: string | number | boolean | null | undefined
    transform?: (value: any) => string
    sx?: React.CSSProperties
  }

  const rows: FieldConfig[][] = [
    [
      {
        key: 'customerName',
        label: 'Customer Name',
        value: selectedCardToView.customerName,
      },
      {
        key: 'phoneNumber',
        label: 'Phone Number',
        value: selectedCardToView.phoneNumber,
      },
      {
        key: 'cif',
        label: 'CIF',
        value: selectedCardToView.cif,
      },
      {
        key: 'productName',
        label: 'Product Name',
        value: selectedCardToView.productName,
      },
    ],
    [
      {
        key: 'pan',
        label: 'Last four PAN digits',
        value: selectedCardToView.pan,
      },
      {
        key: 'cardName',
        label: 'Card Name',
        value: selectedCardToView.customerName,
      },
      {
        key: 'account',
        label: 'Account Associated',
        value: selectedCardToView.account,
        transform: (val) => val || 'N/A',
      },
      {
        key: 'domicileBranch',
        label: 'Domicile Branch',
        value: selectedCardToView.domicileBranch,
      },
    ],
    [
      {
        key: 'idNumber',
        label: 'ID Number',
        value: selectedCardToView.idNumber,
        transform: (val) => val || 'N/A',
      },
      {
        key: 'isPrimary',
        label: 'Card Type',
        value: selectedCardToView.isPrimary,
        transform: (val) => (val ? 'Primary' : 'Supplementary'),
      },

      {
        key: 'isStaff',
        label: 'Tariff',
        value: selectedCardToView.isStaff,
        transform: (val) => (val ? 'Staff' : 'Customer'),
      },
      {
        key: 'status',
        label: 'Status',
        value: selectedCardToView.active,
        transform: (val) => (val ? 'Active' : 'Inactive'),
      },
    ],
    [
      {
        key: 'email',
        label: 'Email',
        value: selectedCardToView.email,
      },
      {
        key: 'postalAddress',
        label: 'Postal Address',
        value: selectedCardToView.postalAddress,
        transform: (val) => val || 'N/A',
      },
      {
        key: 'dateOfBirth',
        label: 'Date of Birth',
        value: formatTimestamp(selectedCardToView.dateOfBirth) || 'N/A',
      },
      {
        key: 'fullPan',
        label: 'Full PAN',
        value: pan ? pan : '123456789012',
      },
    ],
  ]

  const handleShowPan = async () => {
    if (showPan) {
      // If PAN is currently visible, hide it and clear keys
      setShowPan(false)
      clearKeys()
      return
    }

    dispatch(setIsLoadingPan(true))
    const publicKey = await getPublicKey()
    const data = {
      publicKey: publicKey,
      cardIDto: {
        cardIdentifierId: selectedCardToView.cardId,
        cardIdentifierType: 'EXID',
        countryCode: 'KE',
      },
    }
    const encryptedPAN = await getPan(dispatch, data)
    const decryptedPAN = await decryptData(encryptedPAN)
    decryptedPAN && setPan(decryptedPAN)
    dispatch(setIsLoadingPan(false))
    decryptedPAN && setShowPan((prev) => !prev)
  }

  return (
    <Stack
      sx={{ gap: '2rem', border: '1px solid #D0D5DD', borderRadius: '4px' }}
    >
      <Stack
        direction="row"
        sx={{ justifyContent: 'space-between', padding: '1%' }}
      >
        <Stack direction="row" gap="1rem" alignItems="center">
          <Avatar sx={{ backgroundColor: '#FAEAEA' }}>
            <CardHeaderIcon width="30" height="28" stroke="#C02626" />
          </Avatar>
          <Stack>
            <Typography>Credit Card</Typography>
            {selectedCardToView.active ? (
              <CustomActiveChip label="Active" />
            ) : (
              <CustomErrorChip label="Inactive" />
            )}
          </Stack>
        </Stack>
        <Stack direction="row" gap="10px">
          <ResetPinRetriesCard card={selectedCardToView} />
          <ActivateCreditCard card={selectedCardToView} />
        </Stack>
      </Stack>

      <Stack direction="column" spacing={3}>
        {rows.map((row, rowIndex) => (
          <Stack
            key={rowIndex}
            direction="row"
            spacing={rowIndex === 0 ? 1 : 3}
            sx={{ padding: '1rem' }}
          >
            {row.map((field) => {
              const displayValue = field.transform
                ? field.transform(field.value)
                : field.value

              return (
                <ReadOnlyTypography
                  variant="outlined"
                  key={field.key}
                  fullWidth
                  label={field.label}
                  value={displayValue as string}
                  margin="normal"
                  size="small"
                  sx={field.sx}
                  // Conditionally pass slotProps for fullPAN field
                  {...(field.key === 'fullPan' && {
                    slotProps: {
                      input: {
                        type: showPan ? 'text' : 'password',
                        multiline: false,
                        sx: {
                          height: '40px',
                        },
                        endAdornment: (
                          <InputAdornment position="end">
                            <IconButton
                              onClick={handleShowPan}
                              edge="end"
                              loading={isLoadingPan}
                            >
                              {showPan ? <VisibilityOff /> : <Visibility />}
                            </IconButton>
                          </InputAdornment>
                        ),
                      },
                    },
                  })}
                />
              )
            })}
          </Stack>
        ))}
      </Stack>
    </Stack>
  )
}
