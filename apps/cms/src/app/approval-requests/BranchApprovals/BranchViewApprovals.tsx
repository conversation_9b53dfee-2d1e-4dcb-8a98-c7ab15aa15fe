import {
  Box,
  Button,
  Chip,
  ChipProps,
  Paper,
  Stack,
  styled,
  Table,
  TableBody,
  TableContainer,
  TableRow,
  TextField,
  Typography,
} from '@mui/material'
import { useAppDispatch, useAppSelector } from '@/store'
import dayjs from 'dayjs'

import {
  CustomPagination,
  CustomTableCell,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import React, { useEffect, useState } from 'react'
import { sentenceCase } from 'tiny-case'

import { getAllCardsApprovalRequests } from '@/store/actions/ApprovalRequests'
import { LoadingListsSkeleton } from '@dtbx/ui/components'
import { AllApprovalRequestsMoreMenu } from '../MoreMenu'
import { MuiTelInput } from 'mui-tel-input'
  export const RequestChip = styled(Chip)<ChipProps>(() => ({
    padding: '2px 8px',
    borderRadius: '16px',
    background: '#F3F5F5',
    height: '24px',
    width: 'auto',
    minWidth: '0',
  }))
  const headerRow = [
    {
      id: 'requestType',
      label: 'Request Type',
      alignCenter: false,
      alignRight: false,
    },
    {
      id: 'module',
      label: 'Module',
      alignCenter: false,
      alignRight: false,
    },
    {
      id: 'maker',
      label: 'Maker',
      alignCenter: false,
      alignRight: false,
    },
    {
      id: 'maker_timestamp',
      label: 'Maker Timestamp',
      alignCenter: false,
      alignRight: false,
    },
    {
      id: 'action',
      label: 'Action',
      alignCenter: false,
      alignRight: false,
    },
  ]
  
export default function BranchViewApprovals() {
  const { cardApprovalRequests, isLoadingApprovals, cardApprovalsPagination } =
    useAppSelector((state) => state.approvals)
  const dispatch = useAppDispatch()
  const [phone, setPhone] = useState<string>('')
  const [pan, setPan] = useState<string>('')
  const [paginationOptions, setPaginationOptions] = useState({
    page: cardApprovalsPagination?.pageNumber,
    size: cardApprovalsPagination?.pageSize,
    totalPages: cardApprovalsPagination?.totalNumberOfPages,
  })
  
  const handlePagination = async (newOptions: PaginationOptions) => {
    setPaginationOptions(newOptions)
    await getAllCardsApprovalRequests(
      dispatch,
      `status=PENDING&page=${newOptions.page}&size=${newOptions.size}`
    )
  }

  const handleSearch = () => {
    let searchParams = 'status=PENDING&page=1&size=10'

    if (phone) {
      searchParams += `&phoneNumber=${encodeURIComponent(phone)}`
    }

    if (pan) {
      searchParams += `&pan=${encodeURIComponent(pan)}`
    }

    getAllCardsApprovalRequests(dispatch, searchParams)
  }

  const handleClearSearch = () => {
    setPhone('')
    setPan('')
    getAllCardsApprovalRequests(dispatch, 'status=PENDING&page=1&size=10')
  }

  useEffect(() => {
    getAllCardsApprovalRequests(dispatch, 'status=PENDING&page=1&size=10')
  }, [])
  return (
    <Stack sx={{ p: '2%', gap: '1.5vh' }}>
      <Stack direction="row" spacing={2} sx={{ mb: 2 }}>
        <MuiTelInput
          value={phone}
          onChange={(value) => setPhone(value)}
          placeholder="Search by phone number"
          size="small"
          sx={{ minWidth: 200 }}
        />
        <TextField
          value={pan}
          onChange={(e) => setPan(e.target.value)}
          placeholder="Search by PAN"
          size="small"
          sx={{ minWidth: 200 }}
        />
        <Button
          variant="contained"
          onClick={handleSearch}
          disabled={!phone && !pan}
          sx={{ minWidth: 100 }}
        >
          Search
        </Button>
        <Button
          variant="outlined"
          onClick={handleClearSearch}
          sx={{ minWidth: 100 }}
        >
          Clear
        </Button>
      </Stack>
      {isLoadingApprovals ? (
        <LoadingListsSkeleton />
      ) : cardApprovalRequests && cardApprovalRequests.length > 0 ? (
        <Paper
          sx={{
            width: '100%',
            overflow: 'hidden',
            borderRadius: '4px',
            border: '1px solid #EAECF0',
            background: '#FEFEFE',
            boxShadow:
              '0px 1px 2px 0px rgba(16, 24, 40, 0.06), 0px 1px 3px 0px rgba(16, 24, 40, 0.10)',
          }}
          elevation={0}
        >
          <Stack sx={{ px: '1vw', py: '0.5vh' }}>
            <Typography variant="body2">Pending Approval Requests</Typography>
            <Typography variant="label2">
              Showing {cardApprovalRequests?.length} of{' '}
              {cardApprovalsPagination?.totalElements} records
            </Typography>
          </Stack>
            <TableContainer
              component={Paper}
              sx={{
                boxShadow: 'none',
              }}
            >
              <Table
                sx={{ minWidth: 650 }}
                aria-label="pending approvals table"
                size="small"
              >
                <CustomTableHeader
                  order={'asc'}
                  orderBy={'id'}
                  headLabel={headerRow}
                  showCheckbox={false}
                  rowCount={0}
                  numSelected={0}
                  onRequestSort={() => {}}
                  onSelectAllClick={() => {}}
                />
                <TableBody>
                  {cardApprovalRequests &&
                    cardApprovalRequests.map((row, index) => (
                      <TableRow key={index || row.id}>
                        <CustomTableCell sx={{ padding: '10px 24px 10px 16px' }}>
                          <Box>
                            <RequestChip
                              label={sentenceCase(row.makerCheckerType.name)}
                              sx={{ width: 'auto' }}
                            />
                          </Box>
                        </CustomTableCell>
                        <CustomTableCell>
                          {sentenceCase(row.makerCheckerType.module)}
                        </CustomTableCell>
                        <CustomTableCell>{row.maker}</CustomTableCell>
                        <CustomTableCell>
                          {dayjs(row.dateCreated).format('MMMM D, YYYY hh:mm A')}
                        </CustomTableCell>
                        <CustomTableCell>
                          <AllApprovalRequestsMoreMenu request={row} />
                        </CustomTableCell>
                      </TableRow>
                    ))}
                </TableBody>
              </Table>
            </TableContainer>
          <CustomPagination
            options={{
              ...paginationOptions,
              totalPages: cardApprovalsPagination?.totalNumberOfPages,
            }}
            handlePagination={handlePagination}
          />
        </Paper>
      ) : (
        <Stack
          sx={{
            width: '100%',
            justifyContent: 'center',
            alignItems: 'center',
            py: '5vh',
          }}
        >
          <Typography variant="h6" sx={{ textAlign: 'center', mb: 2 }}>
            No approval requests found
          </Typography>
          <Typography variant="body1" sx={{ textAlign: 'center' }}>
            Try adjusting your search criteria or check back later
          </Typography>
        </Stack>
      )}
    </Stack>
  )
}
  