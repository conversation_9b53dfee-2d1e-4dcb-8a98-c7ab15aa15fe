'use client'
import React, { useEffect, useState } from 'react'
import { Divider, Stack } from '@mui/material'
import { useAppDispatch } from '@/store'
import { AntTab, AntTabs, TabPanel } from '@dtbx/ui/components/Tabs'

import Pending from './Pending'
import Rejected from './Rejected'
import { ICardApprovalRequest } from '@/store/interfaces/Approvals'
import { setSelectedCardApprovalRequest } from '@/store/reducers/approvals'
import { getAllCardsApprovalRequests } from '@/store/actions/ApprovalRequests'

export default function ApprovalsPage() {
  const [value, setValue] = useState<number>(0)
  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue)
  }
  const dispatch = useAppDispatch()
  useEffect(() => {
    try {
      dispatch(setSelectedCardApprovalRequest({} as ICardApprovalRequest))
      getAllCardsApprovalRequests(dispatch, `status=PENDING&page=1&size=10`)
    } catch (error) {
      console.error('Error initializing approval requests:', error)
    }
  }, [])
  return (
    <Stack>
      <AntTabs
        sx={{
          marginLeft: '1%',
          borderBottomColor: 'secondary.main',
          '& .MuiTabs-indicator': {
            backgroundColor: 'secondary.main',
          },
        }}
        onChange={handleChange}
        value={value}
        aria-label="approval requests tabs"
      >
        <AntTab
          label={`Pending Requests`}
       
          aria-controls="approval-tabpanel-0"
        />
        <AntTab
          label={`All Requests`}
         
          aria-controls="approval-tabpanel-1"
        />
      </AntTabs>
      <Divider />
      <TabPanel
        value={value}
        index={0}
        id="approval-tabpanel-0"
        aria-labelledby="approval-tab-0"
      >
        <Pending />
      </TabPanel>
      <TabPanel
        value={value}
        index={1}
        id="approval-tabpanel-1"
        aria-labelledby="approval-tab-1"
      >
        <Rejected />
      </TabPanel>
    </Stack>
  )
}
