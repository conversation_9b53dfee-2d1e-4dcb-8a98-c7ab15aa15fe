import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen } from '@testing-library/react'
import { Provider } from 'react-redux'
import { configureStore } from '@reduxjs/toolkit'
import ApprovalsPage from '../../../src/app/approval-requests/page'
import { approvalsReducer } from '../../../src/store/reducers'

// Mock the HasAccessToRights function
vi.mock('@dtbx/store/utils', () => ({
  HasAccessToRights: vi.fn(),
}))

// Mock the BranchViewApprovals component
vi.mock('../../../src/app/approval-requests/BranchApprovals/BranchViewApprovals', () => ({
  default: () => <div data-testid="branch-view-approvals">Branch View Approvals</div>,
}))

// Mock the Pending and Rejected components
vi.mock('../../../src/app/approval-requests/Pending', () => ({
  default: () => <div data-testid="pending-approvals">Pending Approvals</div>,
}))

vi.mock('../../../src/app/approval-requests/Rejected', () => ({
  default: () => <div data-testid="rejected-approvals">Rejected Approvals</div>,
}))

// Mock the actions
vi.mock('../../../src/store/actions/ApprovalRequests', () => ({
  getAllCardsApprovalRequests: vi.fn(),
}))

const createMockStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      approvals: approvalsReducer,
    },
    preloadedState: {
      approvals: {
        cardApprovalRequests: [],
        isLoadingApprovals: false,
        cardApprovalsPagination: {
          pageNumber: 1,
          pageSize: 10,
          totalElements: 0,
          totalNumberOfPages: 0,
        },
        selectedCardApprovalRequest: null,
        ...initialState,
      },
    },
  })
}

const renderWithProvider = (component: React.ReactElement, store = createMockStore()) => {
  return render(
    <Provider store={store}>
      {component}
    </Provider>
  )
}

describe('ApprovalsPage Role-Based Rendering', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should render BranchViewApprovals when user has BRANCH_VIEW_CARDS right', async () => {
    // Mock user having BRANCH_VIEW_CARDS right
    const { HasAccessToRights } = await import('@dtbx/store/utils')
    vi.mocked(HasAccessToRights).mockReturnValue(true)

    renderWithProvider(<ApprovalsPage />)

    // Check that HasAccessToRights was called with correct right
    expect(HasAccessToRights).toHaveBeenCalledWith(['BRANCH_VIEW_CARDS'])
    
    // Check that BranchViewApprovals is rendered
    expect(screen.getByTestId('branch-view-approvals')).toBeInTheDocument()
    
    // Check that the tabbed view is not rendered
    expect(screen.queryByText('Pending Requests')).not.toBeInTheDocument()
    expect(screen.queryByText('All Requests')).not.toBeInTheDocument()
  })

  it('should render original tabbed view when user does not have BRANCH_VIEW_CARDS right', async () => {
    // Mock user not having BRANCH_VIEW_CARDS right
    const { HasAccessToRights } = await import('@dtbx/store/utils')
    vi.mocked(HasAccessToRights).mockReturnValue(false)

    renderWithProvider(<ApprovalsPage />)

    // Check that HasAccessToRights was called with correct right
    expect(HasAccessToRights).toHaveBeenCalledWith(['BRANCH_VIEW_CARDS'])
    
    // Check that the tabbed view is rendered
    expect(screen.getByText('Pending Requests')).toBeInTheDocument()
    expect(screen.getByText('All Requests')).toBeInTheDocument()
    
    // Check that BranchViewApprovals is not rendered
    expect(screen.queryByTestId('branch-view-approvals')).not.toBeInTheDocument()
    
    // Check that the Pending component is rendered by default (first tab)
    expect(screen.getByTestId('pending-approvals')).toBeInTheDocument()
  })

  it('should render tabs correctly in original view', async () => {
    const { HasAccessToRights } = await import('@dtbx/store/utils')
    vi.mocked(HasAccessToRights).mockReturnValue(false)

    renderWithProvider(<ApprovalsPage />)

    // Check that both tabs are present
    const pendingTab = screen.getByText('Pending Requests')
    const allRequestsTab = screen.getByText('All Requests')
    
    expect(pendingTab).toBeInTheDocument()
    expect(allRequestsTab).toBeInTheDocument()
    
    // Check that the first tab is active by default
    expect(screen.getByTestId('pending-approvals')).toBeInTheDocument()
  })
})
