import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render } from '@testing-library/react'
import { Provider } from 'react-redux'
import { configureStore } from '@reduxjs/toolkit'
import CreditCardsLayout from '../../../src/app/credit-cards/layout'
import { cardsReducer } from '../../../src/store/reducers'

// Mock the HasAccessToRights function
vi.mock('@dtbx/store/utils', () => ({
  HasAccessToRights: vi.fn(),
}))

// Mock the CardHeaderIcon
vi.mock('@dtbx/ui/icons', () => ({
  CardHeaderIcon: ({ width, height }: any) => (
    <div data-testid="card-header-icon" style={{ width, height }}>
      Card Icon
    </div>
  ),
}))

const createMockStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      cards: cardsReducer,
    },
    preloadedState: {
      cards: {
        isLoading: false,
        cardsSuccess: false,
        creditCardsList: [],
        creditCardResponse: {},
        selectedCardStatus: 'inactive',
        selectedCardToView: {},
        isBranchListView: false,
        currentTabIndex: 0,
        isLoadingBranchCards: false,
        branchCardsList: [],
        branchCardListPagination: {},
        isLoadingActivateCard: false,
        isLoadingSetPinCard: false,
        isLoadingResetPinRetries: false,
        isLoadingPan: false,
        isLoadingSingleCard: false,
        ...initialState,
      },
    },
  })
}

const renderWithProvider = (component: React.ReactElement, store = createMockStore()) => {
  return render(
    <Provider store={store}>
      {component}
    </Provider>
  )
}

describe('CreditCardsLayout Role-Based View', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should set isBranchListView to true when user has BRANCH_VIEW_CARDS right', async () => {
    // Mock user having BRANCH_VIEW_CARDS right
    const { HasAccessToRights } = await import('@dtbx/store/utils')
    vi.mocked(HasAccessToRights).mockReturnValue(true)
    
    const store = createMockStore()
    
    renderWithProvider(
      <CreditCardsLayout>
        <div>Test Content</div>
      </CreditCardsLayout>,
      store
    )

    // Check that HasAccessToRights was called with correct right
    expect(HasAccessToRights).toHaveBeenCalledWith(['BRANCH_VIEW_CARDS'])
    
    // Check that the store state was updated to show branch view
    const state = store.getState()
    expect(state.cards.isBranchListView).toBe(true)
  })

  it('should set isBranchListView to false when user does not have BRANCH_VIEW_CARDS right', async () => {
    // Mock user not having BRANCH_VIEW_CARDS right
    const { HasAccessToRights } = await import('@dtbx/store/utils')
    vi.mocked(HasAccessToRights).mockReturnValue(false)
    
    const store = createMockStore()
    
    renderWithProvider(
      <CreditCardsLayout>
        <div>Test Content</div>
      </CreditCardsLayout>,
      store
    )

    // Check that HasAccessToRights was called with correct right
    expect(HasAccessToRights).toHaveBeenCalledWith(['BRANCH_VIEW_CARDS'])
    
    // Check that the store state was updated to show ops view
    const state = store.getState()
    expect(state.cards.isBranchListView).toBe(false)
  })

  it('should render the layout with correct title', async () => {
    const { HasAccessToRights } = await import('@dtbx/store/utils')
    vi.mocked(HasAccessToRights).mockReturnValue(true)
    
    const { getByText, getByTestId } = renderWithProvider(
      <CreditCardsLayout>
        <div>Test Content</div>
      </CreditCardsLayout>
    )

    expect(getByText('Credit Cards')).toBeInTheDocument()
    expect(getByTestId('card-header-icon')).toBeInTheDocument()
    expect(getByText('Test Content')).toBeInTheDocument()
  })

  it('should hide the view switch since view is now role-based', async () => {
    const { HasAccessToRights } = await import('@dtbx/store/utils')
    vi.mocked(HasAccessToRights).mockReturnValue(true)
    
    const { container } = renderWithProvider(
      <CreditCardsLayout>
        <div>Test Content</div>
      </CreditCardsLayout>
    )

    // The switch should be hidden (display: none)
    const switchElement = container.querySelector('.MuiFormControlLabel-root')
    expect(switchElement).toHaveStyle({ display: 'none' })
  })
})
