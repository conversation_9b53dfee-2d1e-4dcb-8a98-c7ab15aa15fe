import { describe, it, expect, vi } from 'vitest'
import { sidebarConfig } from '../../src/app/sidebar'

// Mock the icons
vi.mock('@dtbx/ui/icons', () => ({
  RequestsApprovalIcon: () => <div data-testid="requests-approval-icon">RequestsApprovalIcon</div>,
  CreditCardIcon: () => <div data-testid="credit-card-icon">CreditCardIcon</div>,
  DebitCardIcon: () => <div data-testid="debit-card-icon">DebitCardIcon</div>,
  PrepaidCardIcon: () => <div data-testid="prepaid-card-icon">PrepaidCardIcon</div>,
}))

describe('Sidebar Configuration', () => {
  it('should export sidebarConfig array', () => {
    expect(sidebarConfig).toBeDefined()
    expect(Array.isArray(sidebarConfig)).toBe(true)
  })

  it('should have correct number of sidebar items', () => {
    expect(sidebarConfig).toHaveLength(4)
  })

  it('should have Credit Cards configuration', () => {
    const creditCardsItem = sidebarConfig.find(item => item.id === '1')
    
    expect(creditCardsItem).toBeDefined()
    expect(creditCardsItem?.title).toBe('Credit Cards')
    expect(creditCardsItem?.path).toBe('/credit-cards')
    expect(creditCardsItem?.module).toBe('Cards')
    expect(creditCardsItem?.isProductionReady).toBe(true)
    expect(creditCardsItem?.icon).toBeDefined()
  })

  it('should have Debit Cards configuration', () => {
    const debitCardsItem = sidebarConfig.find(item => item.id === '2')
    
    expect(debitCardsItem).toBeDefined()
    expect(debitCardsItem?.title).toBe('Debit Cards')
    expect(debitCardsItem?.path).toBe('/debit-cards')
    expect(debitCardsItem?.module).toBe('Cards')
    expect(debitCardsItem?.isProductionReady).toBe(true)
    expect(debitCardsItem?.icon).toBeDefined()
  })

  it('should have Pre Paid Cards configuration', () => {
    const prepaidCardsItem = sidebarConfig.find(item => item.id === '3')
    
    expect(prepaidCardsItem).toBeDefined()
    expect(prepaidCardsItem?.title).toBe('Pre Paid Cards')
    expect(prepaidCardsItem?.path).toBe('/prepaid-cards')
    expect(prepaidCardsItem?.module).toBe('Cards')
    expect(prepaidCardsItem?.isProductionReady).toBe(true)
    expect(prepaidCardsItem?.icon).toBeDefined()
  })

  it('should have Approval Requests configuration', () => {
    const approvalRequestsItem = sidebarConfig.find(item => item.id === '4')
    
    expect(approvalRequestsItem).toBeDefined()
    expect(approvalRequestsItem?.title).toBe('Approval Requests')
    expect(approvalRequestsItem?.path).toBe('/approval-requests')
    expect(approvalRequestsItem?.module).toBe('Cards')
    expect(approvalRequestsItem?.isProductionReady).toBe(true)
    expect(approvalRequestsItem?.icon).toBeDefined()
  })

  it('should have unique IDs for all items', () => {
    const ids = sidebarConfig.map(item => item.id)
    const uniqueIds = new Set(ids)
    
    expect(uniqueIds.size).toBe(sidebarConfig.length)
  })

  it('should have unique paths for all items', () => {
    const paths = sidebarConfig.map(item => item.path)
    const uniquePaths = new Set(paths)
    
    expect(uniquePaths.size).toBe(sidebarConfig.length)
  })

  it('should have all items marked as production ready', () => {
    const allProductionReady = sidebarConfig.every(item => item.isProductionReady === true)
    
    expect(allProductionReady).toBe(true)
  })

  it('should have all items in Cards module', () => {
    const allCardsModule = sidebarConfig.every(item => item.module === 'Cards')
    
    expect(allCardsModule).toBe(true)
  })

  it('should have valid path format for all items', () => {
    const validPaths = sidebarConfig.every(item => 
      item.path.startsWith('/') && item.path.length > 1
    )
    
    expect(validPaths).toBe(true)
  })

  it('should have non-empty titles for all items', () => {
    const validTitles = sidebarConfig.every(item => 
      item.title && item.title.trim().length > 0
    )
    
    expect(validTitles).toBe(true)
  })

  it('should have icons for all items', () => {
    const allHaveIcons = sidebarConfig.every(item => item.icon !== undefined)
    
    expect(allHaveIcons).toBe(true)
  })

  it('should maintain correct order of items', () => {
    const expectedOrder = ['1', '2', '3', '4']
    const actualOrder = sidebarConfig.map(item => item.id)
    
    expect(actualOrder).toEqual(expectedOrder)
  })

  it('should have correct path structure', () => {
    const expectedPaths = [
      '/credit-cards',
      '/debit-cards', 
      '/prepaid-cards',
      '/approval-requests'
    ]
    const actualPaths = sidebarConfig.map(item => item.path)
    
    expect(actualPaths).toEqual(expectedPaths)
  })

  it('should have correct titles', () => {
    const expectedTitles = [
      'Credit Cards',
      'Debit Cards',
      'Pre Paid Cards',
      'Approval Requests'
    ]
    const actualTitles = sidebarConfig.map(item => item.title)
    
    expect(actualTitles).toEqual(expectedTitles)
  })

  it('should be immutable configuration', () => {
    const originalLength = sidebarConfig.length
    
    // Attempt to modify the array (this should not affect the original)
    const modifiedConfig = [...sidebarConfig, { 
      id: '5', 
      title: 'Test', 
      path: '/test', 
      module: 'Test',
      icon: <div>Test</div>,
      isProductionReady: false 
    }]
    
    expect(sidebarConfig.length).toBe(originalLength)
    expect(modifiedConfig.length).toBe(originalLength + 1)
  })

  it('should have proper TypeScript interface compliance', () => {
    // This test ensures all items conform to ISidebarConfigItem interface
    sidebarConfig.forEach(item => {
      expect(typeof item.id).toBe('string')
      expect(typeof item.title).toBe('string')
      expect(typeof item.path).toBe('string')
      expect(typeof item.module).toBe('string')
      expect(typeof item.isProductionReady).toBe('boolean')
      expect(item.icon).toBeDefined()
    })
  })
})
