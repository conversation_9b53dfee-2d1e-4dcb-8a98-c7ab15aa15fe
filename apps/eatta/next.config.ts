import type { NextConfig } from 'next'

const { NEXT_PUBLIC_EATTA_BUILD } = process.env
const isClient = NEXT_PUBLIC_EATTA_BUILD === 'client'
const nextConfig: NextConfig = {
  basePath: isClient ? '' : '/eatta',
  output: 'standalone',
  distDir: isClient ? '.next-client' : undefined,
  experimental: {
    serverActions: {
      bodySizeLimit: '5mb',
    },
    swcPlugins:
      process.env.NEXT_PUBLIC_TEST === 'e2e'
        ? [
            [
              'swc-plugin-coverage-instrument',
              {
                unstableExclude: ['**/node_modules/**'],
              },
            ],
          ]
        : [],
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'dtbxuatstorageblob.blob.core.windows.net',
        pathname: '/**',
      },
    ],
  },
}

export default nextConfig
