import { PageFilters } from './filters'
import { PaymentChannel } from '@/store/interfaces/Company'

export interface FactoryCreationRequest {
  type: string
  name: string
  bankName: string
  bankBranchName: string
  bankBranchCode: string
  accountNumber: string
  accountCurrency: string
  channel: string
  swiftCode: string
  bankCode: string
  parentOrganizationId: string
}

export interface FactoryResponse {
  channel: PaymentChannel
  id: string
  factoryName: string
  bankName: string
  bankCode: string
  phoneNumber: string
  emailAddress: string
  bankBranchName: string
  bankBranchCode: string
  accountNumber: string
  accountCurrency: string
  swiftCode: string
}

export interface FactoryFilters extends PageFilters {
  name?: string
  dateCreated?: string
  parentOrganizationId: string
}

export interface EditFactoryDetails {
  name?: string
  bankBranchName?: string
  bankCode?: string
  bankName: string
  emailAddress?: string
  phoneNumber?: string
  bankBranchCode?: string
  accountNumber?: string
  accountCurrency?: string
  channel?: string
  swiftCode?: string
}
