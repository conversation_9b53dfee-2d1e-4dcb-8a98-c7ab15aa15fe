import { PageFilters } from '@/store/interfaces/filters'

export type WarehouseFeeStatus = 'Active' | 'Expired' 

export interface GodownsFilters {
  name?: string
  godownCode?: string
  warehouseId?: string
  page?: number
  size?: number
  dateCreated?: string
  ascending?: boolean
}

export interface GodownCreationRequest {
  godownCode: string
  name: string
  warehouseId: string | undefined
}

export interface WarehouseResponse {
  id: string
  godownCode: string
  name: string
  warehouseId: string
}

export interface WarehouseUpdateRequest extends GodownCreationRequest {}

export const PER_UNIT_OPTIONS = ['kgs'] as const

export type UnitType = (typeof PER_UNIT_OPTIONS)[number]

export interface WarehouseFeeFilters extends PageFilters {
  warehouseName?: string
  producerName?: string
  createdBy?: string
  handlingCommission?: string
  storgeCommission?: string
}

export interface WarehouseFee {
  id: string
  producerName: string
  warehouseName: string
  handlingCommission: number
  storageCommission: number
  createdBy: string
  modifiedBy: string
  status: WarehouseFeeStatus
  effectiveDate: string
  unit: string
  dateCreated: string
  dateModified: string
}

export interface WarehouseFeeConfigurationPayload {
  producerId: string
  warehouseId: string
  handlingCommission: number
  unit: UnitType
  effectiveTime: string
}

export interface UpdateWarehouseFeeConfigurationPayload
  extends WarehouseFeeConfigurationPayload {
  id: string
}
