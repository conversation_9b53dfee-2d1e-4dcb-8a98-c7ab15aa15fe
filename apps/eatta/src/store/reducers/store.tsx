import { combineReducers } from 'redux'

import companiesReducer from './companiesReducer'
import cataloguesReducer from './cataloguesReducer'
import brokerCommissionsReducer from './brokerCommissionsReducer'
import {
  authReducer,
  navigation,
  notifications,
  overlays,
} from '@dtbx/store/reducers'
import invoicesReducer from './invoicesReducer'
import insightsReducer from './insightsReducer'
import passwordResetReducer from './passwordResetReducer'
import AuctionScheduleReducer from '@/store/reducers/AuctionScheduleReducer'
import transactionsReducer from './transactionsReducer'
import approvalRequestsReducer from './approvalRequestsReducer'
import edoReducer from './edoReducer'
import warehouseReducer from './warehouseReducer'

const rootReducer = combineReducers({
  companies: companiesReducer,
  navigation: navigation,
  notifications: notifications,
  auth: authReducer,
  overlay: overlays,
  catalogues: cataloguesReducer,
  brokerCommissions: brokerCommissionsReducer,
  invoices: invoicesReducer,
  edo: edoReducer,
  insights: insightsReducer,
  passwordReset: passwordResetReducer,
  salesSchedule: AuctionScheduleReducer,
  transactions: transactionsReducer,
  approvals: approvalRequestsReducer,
  warehouse: warehouseReducer,
})
export type RootReducer = ReturnType<typeof rootReducer>
export default rootReducer
