import React, { useState } from 'react'
import {
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableFooter,
  TableRow,
  Typography,
} from '@mui/material'
import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import { useAppDispatch, useAppSelector } from '@/store'
import { checkIfBackOffice } from '@/utils/appTypeChecker'
import { EditFactoryDialog } from './EditFactoryDialog'
import { setSelectedCompany, setSelectedFactory } from '@/store/reducers'
import { EditFactoryIcon } from '@/components/SvgIcons/FactoryIcon'
import { FactoryResponse } from '@/store/interfaces/factory'
import { getFactories } from '@/store/actions'
import { EmptyPage } from '@dtbx/ui/components/EmptyPage'
import { Order } from '@/store/interfaces'
import TableSkeleton from '@/components/TableSkeleton'

export interface FactoriesTableProps {
  factories: FactoryResponse[]
}

const FactoriesTable = ({ factories }: FactoriesTableProps) => {
  const dispatch = useAppDispatch()
  const [showEditFactory, setShowEditFactory] = useState(false)
  const { selectedCompany, selectedFactory, factoryResponse, isLoading } =
    useAppSelector((state) => state.companies)
  const [order, setOrder] = useState<Order>('asc')
  const [orderBy, setOrderBy] = useState<string>('dateCreated')
  const { decodedToken } = useAppSelector((state) => state.auth)

  const isBackOffice = checkIfBackOffice()

  const [paginationOptions, setPaginationOptions] = useState({
    page: 1,
    size: 10,
    totalPages: factoryResponse.totalNumberOfPages,
  })

  const parentOrganizationId = selectedCompany?.id

  const emptyMessage = isBackOffice
    ? 'No factories found. Please register a new factory to get started.'
    : 'No factory data is currently available. Please contact your broker or call support on 0719 031 888 / 0732 121 888 for assistance.'

  /*************************start pagination handlers***************************/
  const handlePagination = async (newOptions: PaginationOptions) => {
    setPaginationOptions(newOptions)
    await getFactories(dispatch, isBackOffice, {
      ...newOptions,
      parentOrganizationId: parentOrganizationId ?? '',
    })
  }
  /*************************end pagination handlers**************************/

  const handleEditFactory = (factory: FactoryResponse) => {
    dispatch(setSelectedCompany)
    dispatch(setSelectedFactory(factory))
    setShowEditFactory(true)
  }
  const handleCloseEditFactory = () => {
    setShowEditFactory(false)
  }

  return isLoading ? (
    <TableSkeleton rowCount={4} columnCount={4} />
  ) : factoryResponse.data.length === 0 ? (
    <EmptyPage
      title="No records found"
      message={emptyMessage}
      bgUrl={isBackOffice ? '/eatta/combo.svg' : '/combo.svg'}
    />
  ) : (
    <>
      <Typography
        sx={{
          width: '100%',
          textAlign: 'center',
          padding: '1rem',
        }}
      >
        {`Showing ${factoryResponse.totalElements} factories`}
      </Typography>
      <TableContainer
        component={Paper}
        sx={{
          boxShadow: 'none',
        }}
      >
        <Table sx={{ minWidth: 650 }} aria-label="company table" size="small">
          <CustomTableHeader
            order={order}
            orderBy={orderBy}
            headLabel={[
              {
                id: 'factoryName',
                label: 'Mark/Factory',
                alignRight: false,
              },
              {
                id:
                  decodedToken.clientType === 'Producer'
                    ? 'totalWeight'
                    : 'bankName',
                label:
                  decodedToken.clientType === 'Producer'
                    ? 'Total Weight'
                    : 'Bank Name',
                alignRight: false,
              },
              {
                id:
                  decodedToken.clientType === 'Producer'
                    ? 'totalearnings'
                    : 'accountNumber',
                label:
                  decodedToken.clientType === 'Producer'
                    ? 'Total Earnings(USD)'
                    : 'Account Number',
                alignRight: false,
              },
              {
                id: 'icon',
                label: '',
                alignRight: false,
              },
            ]}
            rowCount={factoryResponse.data.length}
            numSelected={0}
          />
          <TableBody>
            {factoryResponse.data.map((row: FactoryResponse) => {
              return (
                <TableRow hover tabIndex={-1}>
                  <TableCell component="th" scope="row">
                    {row.factoryName}
                  </TableCell>
                  <TableCell>
                    <Typography variant="body1">
                      {decodedToken.clientType === 'Producer'
                        ? '-'
                        : row.bankName}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    <Typography variant="body1">
                      {decodedToken.clientType === 'Producer'
                        ? '-'
                        : row.accountNumber}
                    </Typography>
                  </TableCell>
                  <TableCell>
                    {isBackOffice && (
                      <IconButton onClick={() => handleEditFactory(row)}>
                        <EditFactoryIcon />
                      </IconButton>
                    )}
                  </TableCell>
                </TableRow>
              )
            })}
          </TableBody>
          <TableFooter>
            <TableRow>
              <TableCell
                sx={{ paddingInline: 0 }}
                align="center"
                height={40}
                colSpan={12}
              >
                {factoryResponse.totalNumberOfPages > 0 && (
                  <CustomPagination
                    options={{
                      ...paginationOptions,
                      totalPages: factoryResponse.totalNumberOfPages,
                    }}
                    handlePagination={handlePagination}
                  />
                )}
              </TableCell>
            </TableRow>
          </TableFooter>
        </Table>
      </TableContainer>
      <EditFactoryDialog
        open={showEditFactory}
        handleClose={handleCloseEditFactory}
        organizationName={selectedCompany?.name || ''}
        company={selectedCompany!}
        factory={selectedFactory!}
      />
    </>
  )
}

export default FactoriesTable
