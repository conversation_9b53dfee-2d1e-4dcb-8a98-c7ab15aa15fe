import {
  AccordionDetails,
  AccordionSummary,
  Paper,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableRow,
  Typography,
} from '@mui/material'
import { CustomTableHeader } from '@dtbx/ui/components/Table'
import { Catalogue } from '@/store/interfaces'
import ExpandMoreIcon from '@mui/icons-material/ExpandMore'
import { StatusChip } from '@dtbx/ui/components/Chip'
import { CHIP_COLORS } from '@/utils/statusChips'
import { CustomAccordion } from '@/components/CustomAccordion'
import { FC } from 'react'

interface PreAuctionTableProps {
  producer: string
  factory: string
  catalogues: Catalogue[]
}

export const PreAuctionTable: FC<PreAuctionTableProps> = ({
  producer,
  factory,
  catalogues,
}) => {
  return (
    <Stack>
      <CustomAccordion defaultExpanded>
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="pre auction content"
          id="panel1-header"
        >
          <Stack direction="column" justifyContent="flex-start" width="100%">
            <Stack direction="row" alignItems="center" spacing={1}>
              <Typography
                variant="h6"
                sx={{
                  fontWeight: 600,
                  color: '#101828',
                  fontSize: '1.125rem',
                  lineHeight: '1.75rem',
                  textAlign: 'left',
                }}
              >
                {factory}
                <StatusChip
                  label={`${catalogues.length} Lots`}
                  status="success"
                  sx={{
                    marginLeft: 1,
                    backgroundColor: CHIP_COLORS.success.bg,
                    color: CHIP_COLORS.success.label,
                    border: `0.5px solid ${CHIP_COLORS.success.border}`,
                    borderRadius: CHIP_COLORS.success.borderRadius,
                    fontSize: CHIP_COLORS.success.fontSize,
                    fontWeight: CHIP_COLORS.success.fontWeight,
                  }}
                />
              </Typography>
            </Stack>
            <Typography
              variant="subtitle2"
              sx={{
                fontWeight: 400,
                color: '#475467',
                fontSize: '0.875rem',
                textAlign: 'left',
              }}
            >
              Producer: {producer}
            </Typography>
          </Stack>
        </AccordionSummary>
        <AccordionDetails>
          <TableContainer
            component={Paper}
            sx={{
              boxShadow: 'none',
              '& .MuiTableCell-root': {
                paddingInline: '1.5rem',
                paddingBlock: '0.5rem',
                textAlign: 'left',
              },
            }}
          >
            <Table
              sx={{ minWidth: 650 }}
              aria-label="pre auction table"
              size="small"
            >
              <CustomTableHeader
                order={'asc'}
                orderBy={''}
                headLabel={[
                  { id: 'comment', label: 'Comment', alignRight: false },
                  { id: 'lotNo', label: 'Lot', alignRight: false },
                  {
                    id: 'invoiceNo',
                    label: 'Garden Invoice',
                    alignRight: false,
                  },
                  { id: 'producer', label: 'Producer', alignRight: false },
                  { id: 'mark', label: 'Mark', alignRight: false },
                  {
                    id: 'value',
                    label: 'Expected Value',
                    alignRight: false,
                  },
                  { id: 'grade', label: 'Grade', alignRight: false },
                  { id: 'wareHouse', label: 'Warehouse', alignRight: false },
                  { id: 'type', label: 'Type', alignRight: false },
                  { id: 'pkgs', label: 'Pkgs', alignRight: false },
                  { id: 'kgs', label: 'Kgs', alignRight: false },
                  {
                    id: 'netTotal',
                    label: 'Net Total (Kgs)',
                    alignRight: false,
                  },
                ]}
                showCheckbox={false}
                rowCount={catalogues.length}
                numSelected={0}
                onRequestSort={() => {}}
              />
              <TableBody>
                {catalogues.map((row) => {
                  return (
                    <TableRow
                      hover
                      key={row.id}
                      tabIndex={-1}
                      role="checkbox"
                      aria-checked={false}
                    >
                      <TableCell>{row.comment}</TableCell>
                      <TableCell sx={{ display: 'flex', alignItems: 'center' }}>
                        {row.lotNo}
                        {row.rePrint > 0 && (
                          <StatusChip
                            label={`RP ${row.rePrint}`}
                            status={row.rePrint === 1 ? 'warn' : 'error'}
                            sx={{
                              marginLeft: 1,
                              backgroundColor: CHIP_COLORS.warn.bg,
                              color: CHIP_COLORS.warn.label,
                              border: `0.5px solid ${CHIP_COLORS.warn.border}`,
                              borderRadius: CHIP_COLORS.warn.borderRadius,
                              fontSize: CHIP_COLORS.warn.fontSize,
                              fontWeight: CHIP_COLORS.warn.fontWeight,
                            }}
                          />
                        )}
                      </TableCell>
                      <TableCell>{row.invoiceNo}</TableCell>
                      <TableCell>{row.producer}</TableCell>
                      <TableCell>{row.factory}</TableCell>
                      <TableCell>{row.value}</TableCell>
                      <TableCell>{row.grade}</TableCell>
                      <TableCell>{row.wareHouse}</TableCell>
                      <TableCell>{row.type}</TableCell>
                      <TableCell>
                        {new Intl.NumberFormat('en-US').format(row.pkgs)}
                      </TableCell>
                      <TableCell>
                        {new Intl.NumberFormat('en-US').format(row.kgs)}
                      </TableCell>
                      <TableCell>
                        {new Intl.NumberFormat('en-US').format(row.netWeight)}
                      </TableCell>
                    </TableRow>
                  )
                })}
              </TableBody>
            </Table>
          </TableContainer>
        </AccordionDetails>
      </CustomAccordion>
    </Stack>
  )
}
