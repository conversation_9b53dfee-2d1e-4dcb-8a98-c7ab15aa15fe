import { <PERSON><PERSON>, TableCell, TableFooter, TableRow } from '@mui/material'
import { TableBody } from '@mui/material'
import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import { Table } from '@mui/material'
import { Paper } from '@mui/material'
import { TableContainer } from '@mui/material'
import { useState } from 'react'
import {
  Order,
  ProducerInvoiceEntry,
  ProducerInvoiceEntryFilters,
} from '@/store/interfaces'
import { useAppSelector } from '@/store'
import { EmptyPage } from '@dtbx/ui/components/EmptyPage'
import TableSkeleton from '@/components/TableSkeleton'

export interface StatementsTableProps {
  filters: ProducerInvoiceEntryFilters
  onPageChange?: (page: number, size: number) => void
}
export const StatementsTable: React.FC<StatementsTableProps> = ({
  filters,
  onPageChange,
}) => {
  const { producerInvoiceEntryResponse, isLoading } = useAppSelector(
    (state) => state.invoices
  )
  const [order, setOrder] = useState<Order>('asc')
  const [orderBy, setOrderBy] = useState<string>('dateCreated')
  const [paginationOptions, setPaginationOptions] = useState({
    page: filters.page,
    size: filters.size,
    totalPages: producerInvoiceEntryResponse.totalNumberOfPages,
  })

  /*************************start pagination handlers***************************/
  const handlePagination = async (newOptions: PaginationOptions) => {
    setPaginationOptions(newOptions)
    if (onPageChange) {
      onPageChange(newOptions.page, newOptions.size)
    }
  }
  /*************************end pagination handlers**************************/

  return (
    <Stack sx={{ height: '100%' }}>
      {isLoading ? (
        <TableSkeleton rowCount={5} columnCount={4} />
      ) : producerInvoiceEntryResponse.data.length === 0 ? (
        <EmptyPage
          title="No records found"
          message="No statement data available for the selected auction."
          bgUrl={'/combo.svg'}
        />
      ) : (
        <TableContainer
          component={Paper}
          sx={{
            boxShadow: 'none',
            '& .MuiTableCell-root': {
              paddingInline: '1.5rem',
              paddingBlock: '0.5rem',
              textAlign: 'left',
            },
          }}
        >
          <Table
            sx={{ minWidth: 650 }}
            aria-label="Mark Statement"
            size="small"
          >
            <CustomTableHeader
              order={order}
              orderBy={orderBy}
              headLabel={[
                { id: 'factory', label: 'Mark', alignRight: false },
                {
                  id: 'totalExpected',
                  label: 'Total Expected ($)',
                  alignRight: false,
                },
                {
                  id: 'amountReceived',
                  label: 'Amount Received from Buyer ($)',
                  alignRight: false,
                },
                {
                  id: 'amountDisbursed',
                  label: 'Amount Disbursed ($)',
                  alignRight: false,
                },
              ]}
              showCheckbox={false}
              rowCount={0}
              numSelected={0}
            />
            <TableBody>
              {producerInvoiceEntryResponse.data.map(
                (row: ProducerInvoiceEntry) => (
                  <TableRow hover={true} key={row.factory}>
                    <TableCell
                      sx={{
                        fontWeight: 400,
                        color: '#10182',
                        fontSize: '0.875rem',
                      }}
                    >
                      {row.factory}
                    </TableCell>
                    <TableCell
                      sx={{
                        fontWeight: 400,
                        color: '#10182',
                        fontSize: '0.875rem',
                      }}
                    >
                      $
                      {new Intl.NumberFormat('en-US').format(row.totalExpected)}
                    </TableCell>
                    <TableCell
                      sx={{
                        fontWeight: 400,
                        color: '#10182',
                        fontSize: '0.875rem',
                      }}
                    >
                      $
                      {new Intl.NumberFormat('en-US').format(
                        row.amountReceived
                      )}
                    </TableCell>
                    <TableCell
                      sx={{
                        fontWeight: 400,
                        color: '#10182',
                        fontSize: '0.875rem',
                      }}
                    >
                      $
                      {new Intl.NumberFormat('en-US').format(
                        row.amountDisbursed
                      )}
                    </TableCell>
                  </TableRow>
                )
              )}
            </TableBody>
            <TableFooter>
              <TableRow>
                <TableCell
                  sx={{ paddingInline: 0 }}
                  align="center"
                  height={40}
                  colSpan={4}
                >
                  {producerInvoiceEntryResponse.totalNumberOfPages > 0 && (
                    <CustomPagination
                      options={{
                        ...paginationOptions,
                        totalPages:
                          producerInvoiceEntryResponse.totalNumberOfPages,
                      }}
                      handlePagination={handlePagination}
                    />
                  )}
                </TableCell>
              </TableRow>
            </TableFooter>
          </Table>
        </TableContainer>
      )}
    </Stack>
  )
}
