import { Chip, Divider, Stack } from '@mui/material'
import React, { FC } from 'react'
import { AntTab, AntTabs } from '@dtbx/ui/components/Tabs'
import { InvoiceTabType } from '@/store/interfaces'
import { useAppSelector } from '@/store'

export interface InvoiceTabsProps {
  tabs: InvoiceTabType[]
  selectedTab: number
  onTabSelected: (index: number) => void
}

export const InvoiceTabs: FC<InvoiceTabsProps> = ({
  tabs,
  selectedTab,
  onTabSelected,
}) => {
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    onTabSelected(newValue)
  }
  const { isLoading } = useAppSelector((state) => state.invoices)

  return (
    <Stack
      sx={{
        backgroundColor: '#F2F4F7',
      }}
    >
      <AntTabs
        value={selectedTab}
        onChange={handleTabChange}
        aria-label="invoice tabs"
        sx={{
          '& .MuiTabs-flexContainer': {
            gap: '0.5rem',
          },
        }}
      >
        {tabs.map((tab, index) => (
          <AntTab
            key={tab.title}
            value={index}
            label={
              <Stack direction="row" alignItems="center" spacing={1}>
                <span style={{ color: tab.status.label, fontWeight: 600 }}>
                  {tab.title}
                </span>
                {tab.itemCounts !== undefined &&
                  tab.itemCounts > 0 &&
                  !isLoading && (
                    <Chip
                      label={tab.itemCounts}
                      sx={{
                        '& .MuiChip-label': {
                          px: 0.7,
                          py: 0,
                        },
                        height: 'auto',
                        fontSize: '0.75rem',
                        color: tab.status.label,
                        fontWeight: 500,
                        backgroundColor: tab.status.bg,
                        border: `1px solid ${tab.status.border}`,
                      }}
                    />
                  )}
              </Stack>
            }
          />
        ))}
      </AntTabs>
      <Divider sx={{ width: '100%', margin: '0 auto' }} />
    </Stack>
  )
}
