import { AccountSalesEntry } from '@/store/interfaces'
import { formatCurrency } from '@dtbx/store/utils'
import { Typography } from '@mui/material'
import { Box } from '@mui/material'
import { Stack } from '@mui/material'

export interface AccountSalesDetailsProps {
  data: AccountSalesEntry
}

const AccountSalesDetails: React.FC<AccountSalesDetailsProps> = ({ data }) => {
  if (!data) {
    return null
  }
  return (
    <Stack
      sx={{
        width: '100%',
        bgcolor: '#f5f5f5',
        paddingTop: 2,
        paddingInline: 3,
        background: 'white',
        borderTop: '1px solid #EAECF0',
      }}
    >
      <Stack
        direction={{ xs: 'column', sm: 'row' }}
        mb={{ xs: 2, sm: 3 }}
        flexWrap="wrap"
        sx={{
          gap: { md: 5, lg: 0, sm: 2 },
          spacing: { md: 6, lg: 6, sm: 2 },
        }}
      >
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            flex: 0.3,
          }}
        >
          <Typography
            variant="caption"
            sx={{
              mb: 0.5,
              color: '#000A12',
              fontWeight: 600,
              fontSize: '0.75rem',
              textWrap: 'nowrap',
            }}
          >
            Total Pkgs
          </Typography>
          <Typography
            variant="h5"
            sx={{ fontSize: '1.5rem', fontWeight: 600, color: '#000A12' }}
          >
            {formatCurrency(data.totalPkgs, 'USD', 'en-US')}
          </Typography>
        </Box>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            flex: 0.4,
          }}
        >
          <Typography
            variant="caption"
            sx={{
              mb: 0.5,
              color: '#000A12',
              fontWeight: 600,
              fontSize: '0.75rem',
              textWrap: 'nowrap',
            }}
          >
            Total Sold Weight (Kgs)
          </Typography>
          <Typography
            variant="h5"
            sx={{ fontSize: '1.5rem', fontWeight: 600, color: '#000A12' }}
          >
            {formatCurrency(data.totalNetSoldWeight, 'USD', 'en-US')}
          </Typography>
        </Box>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            flex: 0.4,
          }}
        >
          <Typography
            variant="caption"
            sx={{
              mb: 0.5,
              color: '#344054',
              fontWeight: 600,
              fontSize: '0.75rem',
              textWrap: 'nowrap',
            }}
          >
            Gross Value ($)
          </Typography>
          <Typography
            variant="h5"
            sx={{ fontSize: '1.5rem', fontWeight: 600, color: '#000A12' }}
          >
            {formatCurrency(data.totalGrossValue, 'USD', 'en-US')}
          </Typography>
        </Box>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            flex: 0.4,
          }}
        >
          <Typography
            variant="caption"
            sx={{
              mb: 0.5,
              color: '#D92D20',
              fontWeight: 600,
              fontSize: '0.75rem',
              textWrap: 'nowrap',
            }}
          >
            Broker Commission
          </Typography>
          <Typography
            variant="h5"
            sx={{ fontSize: '1.5rem', fontWeight: 600, color: '#D92D20' }}
          >
            {formatCurrency(
              data.totalBrokerCommissionFromProducer,
              'USD',
              'en-US'
            )}
          </Typography>
        </Box>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            flex: 0.5,
          }}
        >
          <Typography
            variant="caption"
            sx={{
              mb: 0.5,
              color: '#344054',
              fontWeight: 600,
              fontSize: '0.75rem',
              textWrap: 'nowrap',
            }}
          >
            WHT (5% Broker comm.) USD
          </Typography>
          <Typography
            variant="h5"
            sx={{ fontSize: '1.5rem', fontWeight: 600, color: '#000A12' }}
          >
            {formatCurrency(
              data.totalBrokerProducerWithholdingTax,
              'USD',
              'en-US'
            )}
          </Typography>
        </Box>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            flex: 0.5,
          }}
        >
          <Typography
            variant="caption"
            sx={{
              mb: 0.5,
              color: '#D92D20',
              fontWeight: 600,
              fontSize: '0.75rem',
              textWrap: 'nowrap',
            }}
          >
            Warehouse Charges ($)
          </Typography>
          <Typography
            variant="h5"
            sx={{ fontSize: '1.5rem', fontWeight: 600, color: '#D92D20' }}
          >
            {formatCurrency(data.totalWareHouseCharges, 'USD', 'en-US')}
          </Typography>
        </Box>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            flex: 0.5,
          }}
        >
          <Typography
            variant="caption"
            sx={{
              mb: 0.5,
              color: '#029327',
              fontWeight: 600,
              fontSize: '0.75rem',
              textWrap: 'nowrap',
            }}
          >
            Net Proceeds Due ($)
          </Typography>
          <Typography
            variant="h5"
            sx={{ fontSize: '1.5rem', fontWeight: 600, color: '#029327' }}
          >
            {formatCurrency(data.totalNetProceedsDue, 'USD', 'en-US')}
          </Typography>
        </Box>
      </Stack>
    </Stack>
  )
}

export default AccountSalesDetails
