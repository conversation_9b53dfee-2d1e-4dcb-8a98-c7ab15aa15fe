/**
 * <AUTHOR> on 01/07/2025
 */
'use client'

import { CustomTabs, TabType } from '@/components/CustomTabs'
import {
  TransactionFilters,
  TransactionStatus,
} from '@/store/interfaces/transactions'
import { Divider, Stack } from '@mui/material'
import { DEFAULT_FILTER_CONFIG, PageFilters } from '@/components/PageFilters'
import { TabPanel } from '@dtbx/ui/components/Tabs'
import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { usePathname, useRouter, useSearchParams } from 'next/navigation'
import { useAppDispatch, useAppSelector } from '@/store'
import { TransactionTable } from '@/features/transactions/TransactionTable'
import { resetTransactions, setTransactionsFilters } from '@/store/reducers'

const TABS: TabType<TransactionStatus>[] = [
  {
    title: 'Pending',
    status: 'PENDING',
    canSelect: false,
  },
  {
    title: 'Successful',
    status: 'SUCCESS',
    canSelect: false,
  },
  {
    title: 'Failed',
    status: 'FAILED',
    canSelect: false,
  },
]

export const TransactionPage = () => {
  const dispatch = useAppDispatch()
  const pathname = usePathname()
  const { replace } = useRouter()
  const searchParams = useSearchParams()
  const initialTab = +(searchParams.get('tab') ?? 0)
  const [tabs, setTabs] = useState<TabType<TransactionStatus>[]>(TABS)
  const [selectedTab, setSelectedTab] = useState(initialTab)

  const { isLoading, filters } = useAppSelector((state) => state.transactions)

  const isPendingTab = useMemo(() => {
    return tabs[selectedTab].title === 'Pending'
  }, [selectedTab, tabs])

  const handleSearch = useCallback((newFilters: TransactionFilters) => {
    dispatch(
      setTransactionsFilters({
        ...newFilters,
        page: 1,
      })
    )
  }, [])

  const handleTabChange = (index: number) => {
    dispatch(resetTransactions())
    setSelectedTab(index)
  }

  const handleCountChange = (
    count: number,
    tab: TabType<TransactionStatus>
  ) => {
    setTabs((prevTabs) =>
      prevTabs.map((t) =>
        t.title === tab.title ? { ...t, itemCounts: count } : t
      )
    )
  }

  const updatePath = (index: number) => {
    const params = new URLSearchParams(searchParams)
    params.set('tab', index.toString())
    replace(`${pathname}?${params.toString()}`)
  }

  useEffect(() => {
    updatePath(selectedTab)
  }, [selectedTab, filters])

  return (
    <Stack sx={{ height: '100%' }}>
      <PageFilters
        title="Money Out - Disbursements"
        subtitle={
          'Showing amounts received for each sale, for participant organizations, and monies paid out to them.'
        }
        filterConfig={{
          ...DEFAULT_FILTER_CONFIG,
          showStatus: false,
          showExport: false,
          showDateFilter: true,
          showDisbursementFilter: true,
          showChannel: true,
          showCompanyType: true,
          showSaleDate: !isPendingTab,
        }}
        onSearch={handleSearch}
        filters={filters}
        companyTypes={['Factory', 'Broker', 'Warehouse']}
        searchByValues={[
          {
            filterLabel: 'Organization Name',
            filterKey: 'organizationName',
            type: 'string',
          },
          {
            filterLabel: 'Account Number',
            filterKey: 'accountNumber',
            type: 'numeric',
          },
        ]}
        onExport={() => console.log('Export clicked')}
      />
      <Divider />
      <CustomTabs
        tabs={tabs}
        selectedTab={selectedTab}
        onTabSelected={handleTabChange}
        isLoading={isLoading}
      />
      <Stack sx={{ overflow: 'auto' }}>
        {tabs.map((tab, index) => (
          <TabPanel key={tab.title} value={selectedTab} index={index}>
            <TransactionTable
              status={tab.status}
              filters={filters}
              canSelect={tabs[selectedTab].canSelect}
              onTransactionCountChange={(count) =>
                handleCountChange(count, tabs[selectedTab])
              }
              onSetCurrentTab={setSelectedTab}
            />
          </TabPanel>
        ))}
      </Stack>
    </Stack>
  )
}
