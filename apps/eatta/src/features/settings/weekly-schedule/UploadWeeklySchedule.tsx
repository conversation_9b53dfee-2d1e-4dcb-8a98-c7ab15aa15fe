import React, { useState } from 'react'
import {
  Button,
  CircularProgress,
  Dialog,
  DialogContent,
  DialogContentText,
  DialogTitle,
  Divider,
  IconButton,
  Stack,
  Typography,
} from '@mui/material'
import * as Yup from 'yup'
import { Form, FormikProvider, useFormik } from 'formik'
import CloseIcon from '@mui/icons-material/Close'
import { FileUpload } from '@/components/FileUpload'
import { useAppDispatch, useAppSelector } from '@/store'
import { uploadSalesSchedule } from '@/store/actions/AuctionSchedule'
import { UploadIcon } from '@dtbx/ui/icons'
import { Dayjs } from 'dayjs'
import CustomDatePicker from '@/components/CustomDatePicker'
import dayjs from 'dayjs'

type UploadDialogProps = {
  years: string[]
  open: boolean
  handleClose: () => void
}

export const UploadWeeklyScheduleDialog = ({
  years,
  open,
  handleClose,
}: UploadDialogProps) => {
  const validationSchema = Yup.object({
    startDate: Yup.string().required('Start date is required'),
    endDate: Yup.string()
      .required('End date is required')
      .test(
        'end-date-validation',
        'End date must be after start date',
        function (value) {
          const { startDate } = this.parent
          if (!startDate || !value) return true
          return new Date(value) >= new Date(startDate)
        }
      ),
  })

  const dispatch = useAppDispatch()
  const { isLoadingSchedules } = useAppSelector((state) => state.salesSchedule)

  const [selectedFile, setSelectedFile] = useState<File | null>(null)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [startDate, setStartDate] = useState<Dayjs | null>(null)
  const [endDate, setEndDate] = useState<Dayjs | null>(null)

  const formik = useFormik({
    initialValues: {
      startDate: '',
      endDate: '',
    },
    validationSchema,
    onSubmit: async () => {
      if (!selectedFile || !startDate || !endDate) {
        console.log('Missing required fields: file, start date, or end date')
        return
      }

      setUploadProgress(0)

      await uploadSalesSchedule(
        dispatch,
        selectedFile,
        startDate.format('YYYY').toString(),
        startDate.format('YYYY-MM-DD'),
        endDate.format('YYYY-MM-DD'),
        {
          onUploadProgress: (progressEvent) => {
            if (progressEvent.total) {
              const progress = Math.round(
                (progressEvent.loaded * 100) / progressEvent.total
              )
              setUploadProgress(progress)
            }
          },
        }
      )

      handleClose()
    },
  })

  const resetForm = () => {
    // Reset all form fields and state
    formik.resetForm()
    setSelectedFile(null)
    setStartDate(null)
    setEndDate(null)
    setUploadProgress(0)
  }

  const handleStartDateChange = (date: string | null) => {
    if (date) {
      const dayjsDate = dayjs(date)
      setStartDate(dayjsDate)
      formik.setFieldValue('startDate', date)

      // If end date is before the new start date, clear it
      if (endDate && endDate.isBefore(dayjsDate, 'day')) {
        setEndDate(null)
        formik.setFieldValue('endDate', '')
      }
    } else {
      setStartDate(null)
      formik.setFieldValue('startDate', '')
    }
  }

  const handleEndDateChange = (date: string | null) => {
    if (date) {
      const dayjsDate = dayjs(date)
      setEndDate(dayjsDate)
      formik.setFieldValue('endDate', date)
    } else {
      setEndDate(null)
      formik.setFieldValue('endDate', '')
    }
  }

  const handleCancelClick = () => {
    resetForm()
    handleClose()
  }

  return (
    <Dialog maxWidth="sm" open={open} onClose={handleClose}>
      <DialogTitle>
        <Stack
          direction="row"
          justifyContent="space-between"
          alignItems="center"
        >
          <Typography variant="subtitle1" fontWeight="bold">
            Upload New Schedule of Weekly Sales.
          </Typography>
          <IconButton
            onClick={handleClose}
            sx={{
              height: '2rem',
              width: '2rem',
              border: '1px solid #D0D5DD',
              borderRadius: '0.5rem',
            }}
          >
            <CloseIcon />
          </IconButton>
        </Stack>
      </DialogTitle>
      <Divider sx={{ margin: 0 }} />
      <DialogContent>
        <DialogContentText sx={{ mb: 2 }}>
          Please ensure that you use the schedule template to ensure the dates
          are accurate.{' '}
          <a href="/eatta/templates/schedule.xlsx" download>
            Download Template
          </a>
        </DialogContentText>

        <FormikProvider value={formik}>
          <Form onSubmit={formik.handleSubmit}>
            <Stack spacing={3} marginTop={2}>
              <Stack sx={{ width: { xs: '100%', sm: 'auto' } }}>
                <Typography fontWeight="bolder">Start Date:</Typography>
                <CustomDatePicker
                  hiddenLabel={true}
                  onDateChanged={(date: string | null) => {
                    handleStartDateChange(date)
                  }}
                  label="Select Start Date"
                  required={true}
                  value={startDate ? startDate.format('YYYY-MM-DD') : ''}
                  error={
                    !!(formik.touched.startDate && formik.errors.startDate)
                  }
                  helperText={
                    formik.touched.startDate && formik.errors.startDate
                      ? formik.errors.startDate
                      : undefined
                  }
                />
                <Typography variant="caption">
                  Refers to the <strong>catalogue closing date</strong> listed
                  under the first auction in the EATTA schedule.
                </Typography>
              </Stack>

              <Stack sx={{ width: { xs: '100%', sm: 'auto' } }}>
                <Typography fontWeight="bolder">End Date:</Typography>
                <CustomDatePicker
                  hiddenLabel={true}
                  onDateChanged={(date: string | null) => {
                    handleEndDateChange(date)
                  }}
                  label="Select End Date"
                  required={true}
                  value={endDate ? endDate.format('YYYY-MM-DD') : ''}
                  error={!!(formik.touched.endDate && formik.errors.endDate)}
                  helperText={
                    formik.touched.endDate && formik.errors.endDate
                      ? formik.errors.endDate
                      : undefined
                  }
                  minDate={startDate ? startDate : undefined}
                />
                <Typography variant="caption">
                  Refers to the <strong>prompt date </strong>listed under the
                  last auction in the EATTA schedule.
                </Typography>
              </Stack>

              <FileUpload
                progress={uploadProgress}
                disabled={isLoadingSchedules}
                onFileChange={(file) => setSelectedFile(file)}
              />
            </Stack>

            <Stack direction="row" spacing={3} mt={4}>
              <Button
                fullWidth
                variant="contained"
                onClick={handleCancelClick}
                sx={{ background: '#D92D20' }}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                fullWidth
                variant="contained"
                startIcon={
                  isLoadingSchedules ? (
                    <CircularProgress size={20} color="inherit" />
                  ) : (
                    <UploadIcon />
                  )
                }
                disabled={isLoadingSchedules || !selectedFile}
                sx={{
                  bgcolor: selectedFile ? 'primary.main' : '#FFF',
                  border: '1px solid #E4E7EC',
                  color: selectedFile ? '#FFFFFF' : '#98A2B3',
                  fontWeight: 600,
                  '&:hover': {
                    bgcolor: 'primary.main',
                    color: '#FFFFFF',
                    '& svg': {
                      stroke: '#FFFFFF',
                    },
                  },
                }}
              >
                {isLoadingSchedules ? 'Uploading...' : 'Upload Schedule'}
              </Button>
            </Stack>
          </Form>
        </FormikProvider>
      </DialogContent>
    </Dialog>
  )
}
