/**
 * <AUTHOR> on 10/07/2025
 */
import TableSkeleton from '@/components/TableSkeleton'
import { EmptyPage } from '@dtbx/ui/components/EmptyPage'
import {
  Button,
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableFooter,
  TableRow,
} from '@mui/material'
import AddIcon from '@mui/icons-material/Add'
import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import {
  Order,
  PageFilters,
  WarehouseFee,
  WarehouseFeeFilters,
  WarehouseFeeStatus,
} from '@/store/interfaces'

import React, { FC, useEffect, useState } from 'react'
import { useAppDispatch, useAppSelector } from '@/store'
import { sortData } from '@/utils/sortTableData'
import { getWarehouseFeeConfigs } from '@/store/actions'
import { formatCurrency, formatDate } from '@dtbx/store/utils'
import { StatusChip } from '@dtbx/ui/components/Chip'
import { ArrowForwardIos } from '@mui/icons-material'
import ConfigureWarehouseFormDialog from './create/ConfigureWarehouseFormDialog'
import { WAREHOUSE_FEE_STATUS_COLOR } from '@/utils/statusChips'

const initialPageFilters: PageFilters = {
  page: 1,
  size: 10,
}

interface WarehouseFeeTableProps {
  filters: WarehouseFeeFilters
}

export const WarehouseFeeTable: FC<WarehouseFeeTableProps> = ({ filters }) => {
  const dispatch = useAppDispatch()
  const { warehouseFeesResponse, isLoading } = useAppSelector(
    (state) => state.warehouse
  )

  const [order, setOrder] = useState<Order>('desc')
  const [orderBy, setOrderBy] = useState<keyof WarehouseFee>('dateCreated')
  const [openModal, setOpenModal] = useState(false)
  const [selectedWarehouseFee, setSelectedWarehouseFee] =
    useState<WarehouseFee | null>(null)

  const handleRequestSort = (
    _event: React.MouseEvent<unknown>,
    property: string
  ) => {
    const isAsc = orderBy === property && order === 'asc'
    const newOrder = isAsc ? 'desc' : 'asc'
    setOrder(newOrder)
    setOrderBy(property as keyof WarehouseFee)
  }

  const sortKey = orderBy as keyof WarehouseFee
  const warehouseFeeDataSorted = sortData(
    [...warehouseFeesResponse.data],
    sortKey,
    order
  )

  const [paginationOptions, setPaginationOptions] = useState(initialPageFilters)
  const handlePagination = async (newOptions: PaginationOptions) => {
    setPaginationOptions({ page: newOptions.page, size: newOptions.size })
  }

  const handleSetUpNewFeeConfig = () => {
    setSelectedWarehouseFee(null)
    setOpenModal(true)
  }

  const handleConfigDetail = (warehouseFee: WarehouseFee) => {
    setSelectedWarehouseFee(warehouseFee)
    setOpenModal(true)
  }

  const handleCloseModal = () => {
    setOpenModal(false)
    setSelectedWarehouseFee(null)
  }

  useEffect(() => {
    const fetchWareHouseFees = async () => {
      await getWarehouseFeeConfigs(dispatch, {
        ...filters,
        page: paginationOptions.page,
        size: paginationOptions.size,
        ascending: false,
      })
    }
    fetchWareHouseFees()
  }, [dispatch, filters, paginationOptions.page])

  return (
    <>
      {isLoading ? (
        <TableSkeleton rowCount={15} columnCount={6} />
      ) : warehouseFeesResponse.data.length === 0 ? (
        <EmptyPage
          title="No Warehouse fee configuration found"
          message="Please set up new fee configuration to get started"
          bgUrl="/eatta/combo.svg"
          action={
            <Button
              variant="contained"
              type="submit"
              startIcon={<AddIcon />}
              onClick={handleSetUpNewFeeConfig}
            >
              Setup New Fee Config
            </Button>
          }
        />
      ) : (
        <TableContainer
          component={Paper}
          sx={{
            boxShadow: 'none',
            '& .MuiTableCell-root': {
              paddingInline: '1.5rem',
              paddingBlock: '0.5rem',
              textAlign: 'left',
            },
          }}
        >
          <Table
            sx={{ minWidth: 650 }}
            aria-label="Warehouse fee table"
            size="small"
          >
            <CustomTableHeader
              order={order}
              orderBy={orderBy}
              headLabel={[
                {
                  id: 'warehouseName',
                  label: 'Warehouse',
                  alignRight: false,
                },
                { id: 'producerName', label: 'Producer', alignRight: false },
                {
                  id: 'handlingCommission',
                  label: 'Handling Fees ($)',
                  alignRight: false,
                },
                {
                  id: 'unit',
                  label: 'Unit',
                  alignRight: false,
                },
                {
                  id: 'effectiveDate',
                  label: 'Effective Date',
                  alignRight: false,
                },
                {
                  id: 'createdBy',
                  label: 'Created By',
                  alignRight: false,
                },
                {
                  id: 'updatedBy',
                  label: 'Updated By',
                  alignRight: false,
                },
                {
                  id: 'modifiedDate',
                  label: 'Date Last Modified',
                  alignRight: false,
                },
                {
                  id: 'status',
                  label: 'Status',
                  alignRight: false,
                },
                { id: '', label: '', alignRight: false },
              ]}
              showCheckbox={false}
              rowCount={warehouseFeesResponse.data.length}
              numSelected={0}
              onRequestSort={handleRequestSort}
            />
            <TableBody>
              {warehouseFeeDataSorted.map((row: WarehouseFee) => {
                const {
                  id,
                  producerName,
                  warehouseName,
                  handlingCommission,
                  createdBy,
                  status,
                  effectiveDate,
                  unit,
                  dateModified,
                  modifiedBy,
                } = row

                return (
                  <TableRow hover key={id} tabIndex={-1} role="checkbox">
                    <TableCell>{warehouseName}</TableCell>
                    <TableCell>{producerName}</TableCell>
                    <TableCell>
                      {formatCurrency(handlingCommission, 'USD', 'en-US')}
                    </TableCell>
                    <TableCell>{unit}</TableCell>
                    <TableCell>{formatDate(effectiveDate)}</TableCell>
                    <TableCell>{createdBy || '-'}</TableCell>
                    <TableCell>{modifiedBy || '-'}</TableCell>
                    <TableCell>{formatDate(dateModified)}</TableCell>
                    <TableCell>
                      <StatusChip
                        label={status}
                        status={
                          WAREHOUSE_FEE_STATUS_COLOR[
                            status as WarehouseFeeStatus
                          ]
                        }
                      />
                    </TableCell>
                    <TableCell>
                      <IconButton onClick={() => handleConfigDetail(row)}>
                        <ArrowForwardIos />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                )
              })}
            </TableBody>
            <TableFooter>
              <TableRow>
                <TableCell
                  sx={{ paddingInline: 0 }}
                  align="center"
                  height={40}
                  colSpan={12}
                >
                  {warehouseFeesResponse.totalNumberOfPages > 0 && (
                    <CustomPagination
                      options={{
                        ...paginationOptions,
                        totalPages: warehouseFeesResponse.totalNumberOfPages,
                      }}
                      handlePagination={handlePagination}
                    />
                  )}
                </TableCell>
              </TableRow>
            </TableFooter>
          </Table>
        </TableContainer>
      )}
      <ConfigureWarehouseFormDialog
        open={openModal}
        onClose={handleCloseModal}
        warehouseFee={selectedWarehouseFee}
      />
    </>
  )
}
