import { Button, CircularProgress, Divider, IconButton } from '@mui/material'
import { Stack, DialogContent, DialogTitle, Typography } from '@mui/material'
import { Dialog } from '@mui/material'
import { useState } from 'react'
import { ConfirmCancel } from '@/components/ConfirmCancel'
import CloseIcon from '@mui/icons-material/Close'
import {
  WarehouseFeeConfigurationPayload,
  WarehouseFee,
} from '@/store/interfaces/warehouse'
import { useAppDispatch, useAppSelector } from '@/store'
import {
  configureWarehouseFee,
  updateWarehouseFee,
} from '@/store/actions/warehouse'
import ChevronRight from '@mui/icons-material/ChevronRight'

interface ConfirmWarehouseFeeProps {
  open: boolean
  onClose: () => void
  onConfirm: (success?: boolean) => void
  formData: WarehouseFeeConfigurationPayload | null
  warehouseFee?: WarehouseFee | null
}

const ConfirmFeeConfiguration = ({
  open,
  onClose,
  onConfirm,
  formData,
  warehouseFee,
}: ConfirmWarehouseFeeProps) => {
  const dispatch = useAppDispatch()
  const { isConfiguringWarehouseFee } = useAppSelector(
    (state) => state.warehouse
  )
  const { companiesResponse } = useAppSelector((state) => state.companies)

  const [showConfirmDialog, setShowConfirmDialog] = useState(false)

  const isEditMode = Boolean(warehouseFee)

  const getCompanyNameById = (
    id: string,
    type: 'Warehouse' | 'Producer'
  ): string => {
    const company = companiesResponse.data.find(
      (company) => company.id === id && company.type === type
    )
    return company?.name || `Unknown ${type}`
  }

  const handleShowConfirmDialog = () => {
    setShowConfirmDialog(true)
  }

  const handleConfirmDiscard = () => {
    setShowConfirmDialog(false)
    onClose()
  }

  const handleSave = async () => {
    if (formData) {
      if (isEditMode && warehouseFee) {
        const updatePayload = {
          ...formData,
          id: warehouseFee.id,
        }
        await updateWarehouseFee(dispatch, updatePayload, onConfirm)
      } else {
        await configureWarehouseFee(dispatch, formData, onConfirm)
      }
    }
  }

  return (
    <Dialog
      fullWidth
      maxWidth="sm"
      open={open}
      onClose={handleShowConfirmDialog}
    >
      <Stack
        spacing={2}
        sx={{
          bgcolor: '#FFFFFF',
          width: '100%',
        }}
      >
        <DialogTitle fontWeight={600}>
          <Stack spacing={1}>
            <Stack
              direction="row"
              justifyContent="space-between"
              alignItems="center"
            >
              <Typography
                variant="h5"
                fontWeight="bold"
                sx={{ fontSize: '1.5rem', fontWeight: 600, color: '#000A12' }}
              >
                {isEditMode
                  ? 'Confirm fee update'
                  : 'Confirm fee configuration'}
              </Typography>
              <IconButton
                sx={{
                  height: '2rem',
                  width: '2rem',
                  border: '1px solid #D0D5DD',
                  borderRadius: '0.5rem',
                }}
                onClick={() => onClose()}
              >
                <CloseIcon />
              </IconButton>
            </Stack>
          </Stack>
        </DialogTitle>
        <Divider sx={{ '&.MuiDivider-root': { margin: 0 } }} />
        <DialogContent>
          <Stack
            spacing={3}
            sx={{
              width: '100%',
              mb: 3,
            }}
          >
            <Typography
              sx={{ fontSize: '1rem', fontWeight: 600, color: '#344054' }}
            >
              Handling Fee:
            </Typography>

            <Typography
              sx={{ fontSize: '1.5rem', fontWeight: 600, color: '#000A12' }}
            >
              {formData
                ? `${formData.handlingCommission} USD/${formData.unit}`
                : '0.00 USD/kgs'}
            </Typography>

            <Stack direction="column" spacing={1} sx={{ mt: 2 }}>
              <Typography
                sx={{ fontSize: '1rem', fontWeight: 600, color: '#344054' }}
              >
                Effective Date
              </Typography>

              <Typography
                sx={{ fontSize: '1rem', fontWeight: 400, color: '#667085' }}
              >
                {formData?.effectiveTime || ''}
              </Typography>
            </Stack>
          </Stack>

          <Stack
            direction="row"
            justifyContent="space-between"
            alignItems="flex-start"
            width="100%"
            mb={4}
            mt={2}
          >
            <Stack direction="column" spacing={1}>
              <Typography
                sx={{
                  fontSize: '1rem',
                  fontWeight: 600,
                  color: '#344054',
                  textAlign: 'start',
                }}
              >
                Warehouse
              </Typography>
              <Typography
                sx={{
                  fontSize: '1rem',
                  fontWeight: 400,
                  color: '#667085',
                  textAlign: 'start',
                }}
              >
                {formData?.warehouseId
                  ? getCompanyNameById(formData.warehouseId, 'Warehouse')
                  : ''}
              </Typography>
            </Stack>
            <Stack direction="column" spacing={1}>
              <Typography
                sx={{
                  fontSize: '1rem',
                  fontWeight: 600,
                  color: '#344054',
                  textAlign: 'start',
                }}
              >
                Producer
              </Typography>
              <Typography
                sx={{
                  fontSize: '1rem',
                  fontWeight: 400,
                  color: '#667085',
                  textAlign: 'start',
                }}
              >
                {formData?.producerId
                  ? getCompanyNameById(formData.producerId, 'Producer')
                  : ''}
              </Typography>
            </Stack>
          </Stack>

          <Stack
            direction="row"
            spacing={2}
            sx={{
              display: 'flex',
              alignItems: 'flex-start',
              gap: '5px',
              mb: 4,
            }}
          >
            <Typography
              variant="body2"
              sx={{ fontSize: '1rem', fontWeight: 600, color: '#667085' }}
            >
              The handling fee will be configured for the selected warehouse and
              producer. Once saved, the rate will now apply automatically during
              invoicing or settlement.
            </Typography>
          </Stack>
          <Stack direction="row" spacing={2} sx={{ mt: 2 }}>
            <Button
              variant="outlined"
              fullWidth
              disabled={isConfiguringWarehouseFee}
              onClick={handleShowConfirmDialog}
              sx={{
                mt: 2,
              }}
            >
              {'Cancel'}
            </Button>

            <Button
              variant="contained"
              fullWidth
              disabled={isConfiguringWarehouseFee}
              onClick={handleSave}
              sx={{
                mt: 2,
                textWrap: 'nowrap',
              }}
              endIcon={
                isConfiguringWarehouseFee ? (
                  <CircularProgress size={20} thickness={3.0} />
                ) : (
                  <ChevronRight />
                )
              }
            >
              {'Save Fee Configuration'}
            </Button>
          </Stack>
        </DialogContent>
      </Stack>
      <ConfirmCancel
        open={showConfirmDialog}
        title="Discard Changes"
        description="Are you sure you want to discard your changes?"
        confirmLabel="Discard"
        cancelLabel="Keep Editing"
        onConfirm={handleConfirmDiscard}
        onCancel={() => setShowConfirmDialog(false)}
      />
    </Dialog>
  )
}

export default ConfirmFeeConfiguration
