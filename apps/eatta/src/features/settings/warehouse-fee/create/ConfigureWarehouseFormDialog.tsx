import { ConfirmCancel } from '@/components/ConfirmCancel'
import {
  <PERSON>ton,
  DialogContent,
  Dialog,
  Stack,
  CircularProgress,
  DialogTitle,
  IconButton,
  Divider,
  Box,
  TextField,
} from '@mui/material'
import { Typography } from '@mui/material'
import { useState, useCallback, useEffect } from 'react'
import CloseIcon from '@mui/icons-material/Close'
import { FormikProvider, useFormik } from 'formik'
import { Form } from 'formik'
import * as Yup from 'yup'
import CustomDatePicker from '@/components/CustomDatePicker'
import { PerUnitSelector } from '@/components/PerUnitSelector'
import {
  UnitType,
  WarehouseFeeConfigurationPayload,
  WarehouseFee,
} from '@/store/interfaces/warehouse'
import { CompanySelector } from '@/components/CompanySelector'
import { useAppSelector, useAppDispatch } from '@/store'
import ConfirmFeeConfiguration from './ConfirmWarehouseFee'
import ChevronRight from '@mui/icons-material/ChevronRight'
import { Company } from '@/store/interfaces'
import { getCompanies } from '@/store/actions'
import { DECIMAL_NUMERIC_REGEX } from '@/utils/validators'
import dayjs from 'dayjs'

interface ConfigureWarehouseFeeProps {
  open: boolean
  onClose: () => void
  warehouse?: string
  producer?: string
  date?: string
  warehouseFee?: WarehouseFee | null
}

const ConfigureWarehouseForm = ({
  open,
  onClose,
  warehouse,
  producer,
  date,
  warehouseFee,
}: ConfigureWarehouseFeeProps) => {
  const dispatch = useAppDispatch()
  const { isConfiguringWarehouseFee } = useAppSelector(
    (state) => state.warehouse
  )
  const { companiesResponse } = useAppSelector((state) => state.companies)
  const [showFeeConfirmDialog, setShowFeeConfirmDialog] = useState(false)
  const [showDiscardDialog, setShowDiscardDialog] = useState(false)
  const [formData, setFormData] =
    useState<WarehouseFeeConfigurationPayload | null>(null)
  const [showMainDialog, setShowMainDialog] = useState(true)

  const isEditMode = Boolean(warehouseFee)

  useEffect(() => {
    if (open) {
      getCompanies(dispatch, { page: 1, size: 200 })
    }
  }, [dispatch, open])

  const findCompanyByCode = useCallback(
    (code: string, type: 'Warehouse' | 'Producer'): Company | null => {
      return (
        companiesResponse.data.find(
          (company) => company.code === code && company.type === type
        ) || null
      )
    },
    [companiesResponse.data]
  )

  const findCompanyByName = useCallback(
    (name: string, type: 'Warehouse' | 'Producer'): Company | null => {
      return (
        companiesResponse.data.find(
          (company) => company.name === name && company.type === type
        ) || null
      )
    },
    [companiesResponse.data]
  )

  const findCompanyById = (
    id: string,
    type: 'Warehouse' | 'Producer'
  ): string | null => {
    const company = companiesResponse.data.find(
      (company) => company.id === id && company.type === type
    )
    return company?.code || null
  }

  const getInitialValues = useCallback(() => {
    if (isEditMode && warehouseFee) {
      const warehouseCompany = findCompanyByName(
        warehouseFee.warehouseName,
        'Warehouse'
      )
      const producerCompany = findCompanyByName(
        warehouseFee.producerName,
        'Producer'
      )

      return {
        warehouseId: warehouseCompany?.id || warehouse || '',
        producerId: producerCompany?.id || producer || '',
        handlingCommission: warehouseFee.handlingCommission?.toString() || '',
        unit: (warehouseFee.unit as UnitType) || ('kgs' as UnitType),
        effectiveTime: warehouseFee.effectiveDate || date || '',
      }
    }

    return {
      warehouseId: warehouse || '',
      producerId: producer || '',
      handlingCommission: '',
      unit: 'kgs' as UnitType,
      effectiveTime: date || '',
    }
  }, [isEditMode, warehouseFee, warehouse, producer, date, findCompanyByName])

  const handleShowDiscardDialog = () => {
    setShowDiscardDialog(true)
  }

  const handleConfirmDiscard = () => {
    setShowDiscardDialog(false)
    formik.resetForm()
    onClose()
  }

  const handleDateChange = (date: string | null) => {
    formik.setFieldValue('effectiveTime', date)
  }

  const handleConfirmDialogClose = () => {
    setShowFeeConfirmDialog(false)
    setFormData(null)
    setShowMainDialog(true)
    formik.resetForm()
    onClose()
  }

  const validationSchema = Yup.object({
    warehouseId: Yup.string().required('Warehouse is required'),
    producerId: Yup.string().required('Producer is required'),
    handlingCommission: Yup.string()
      .required('Handling commission is required')
      .matches(DECIMAL_NUMERIC_REGEX, 'Handling commission must be a number')
      .test('min-value', 'Handling commission should not be less than 0', (value) => {
        return value ? parseFloat(value) > 0 : true
      })
      .test('max-value', 'Handling commission should not be more than 10', (value) => {
        return value ? parseFloat(value) < 10 : true
      }),
    unit: Yup.mixed().oneOf(['kgs']).required('Unit is required'),
    effectiveTime: Yup.string().required('Date is required'),
  })

  const formik = useFormik({
    initialValues: getInitialValues(),
    validationSchema,
    validateOnMount: true,
    enableReinitialize: true,
    onSubmit: async (values) => {
      try {
        const payload: WarehouseFeeConfigurationPayload = {
          warehouseId: values.warehouseId,
          producerId: values.producerId,
          handlingCommission: parseFloat(values.handlingCommission),
          unit: values.unit as UnitType,
          effectiveTime: values.effectiveTime,
        }
        setFormData(payload)
        setShowMainDialog(false)
        setShowFeeConfirmDialog(true)
        formik.resetForm()
      } catch (error) {
        console.error('Error during form submission:', error)
      }
    },
  })

  return (
    <>
      <Dialog
        fullWidth
        maxWidth="sm"
        open={open && showMainDialog}
        onClose={() => {
          formik.resetForm()
          onClose()
        }}
      >
        <Stack
          spacing={2}
          sx={{
            bgcolor: '#FFFFFF',
            width: '100%',
          }}
        >
          <DialogTitle fontWeight={600}>
            <Stack spacing={1}>
              <Stack
                direction="row"
                justifyContent="space-between"
                alignItems="center"
              >
                <Typography
                  variant="h5"
                  fontWeight="bold"
                  sx={{ fontSize: '1.5rem', fontWeight: 600, color: '#000A12' }}
                >
                  {isEditMode ? 'Edit' : 'Configure'} fees between warehouse and
                  producer
                </Typography>
                <IconButton
                  sx={{
                    height: '2rem',
                    width: '2rem',
                    border: '1px solid #D0D5DD',
                    borderRadius: '0.5rem',
                  }}
                  onClick={() => {
                    formik.resetForm()
                    onClose()
                  }}
                >
                  <CloseIcon />
                </IconButton>
              </Stack>
            </Stack>
          </DialogTitle>
          <Divider sx={{ '&.MuiDivider-root': { margin: 0 } }} />
          <DialogContent>
            <FormikProvider value={formik}>
              <Form onSubmit={formik.handleSubmit}>
                <Stack
                  spacing={3}
                  sx={{
                    width: '100%',
                    mb: 3,
                  }}
                >
                  <Stack direction="column" spacing={4}>
                    <CompanySelector
                      type="Warehouse"
                      label="Select Warehouse*"
                      placeholder="Select Warehouse.."
                      value={
                        formik.values.warehouseId
                          ? findCompanyById(
                              formik.values.warehouseId,
                              'Warehouse'
                            )
                          : null
                      }
                      onChange={(code) => {
                        if (code) {
                          const company = findCompanyByCode(code, 'Warehouse')
                          formik.setFieldValue(
                            'warehouseId',
                            company?.id || null
                          )
                        } else {
                          formik.setFieldValue('warehouseId', null)
                        }
                      }}
                      error={
                        formik.touched.warehouseId &&
                        Boolean(formik.errors.warehouseId)
                      }
                      helperText={
                        isEditMode && warehouseFee?.warehouseName
                          ? `Previously selected: ${warehouseFee.warehouseName}`
                          : (formik.touched.warehouseId &&
                              formik.errors.warehouseId) ||
                            ''
                      }
                      disabled={isEditMode}
                    />

                    <CompanySelector
                      type="Producer"
                      label="Select Producer*"
                      placeholder="Select Producer.."
                      value={
                        formik.values.producerId
                          ? findCompanyById(
                              formik.values.producerId,
                              'Producer'
                            )
                          : null
                      }
                      onChange={(code) => {
                        if (code) {
                          const company = findCompanyByCode(code, 'Producer')
                          formik.setFieldValue(
                            'producerId',
                            company?.id || null
                          )
                        } else {
                          formik.setFieldValue('producerId', null)
                        }
                      }}
                      error={
                        formik.touched.producerId &&
                        Boolean(formik.errors.producerId)
                      }
                      helperText={
                        isEditMode && warehouseFee?.producerName
                          ? `Previously selected: ${warehouseFee.producerName}`
                          : (formik.touched.producerId &&
                              formik.errors.producerId) ||
                            ''
                      }
                      disabled={isEditMode}
                    />
                  </Stack>
                </Stack>
                <Stack
                  direction="column"
                  spacing={2}
                  sx={{
                    display: 'flex',
                    alignItems: 'flex-start',
                    gap: '5px',
                    mb: 4,
                  }}
                >
                  <Typography
                    variant="body2"
                    sx={{
                      fontSize: '1rem',
                      fontWeight: 600,
                      color: '#344054)',
                    }}
                  >
                    Set Handling Fees
                  </Typography>
                  <Typography
                    variant="body2"
                    sx={{ fontSize: '1rem', fontWeight: 400, color: '#667085' }}
                  >
                    Set specific rates that apply to handling services for this
                    warehouse-producer pair.
                  </Typography>
                </Stack>
                <Stack
                  spacing={3}
                  sx={{
                    width: '100%',
                    mb: 3,
                  }}
                >
                  <Stack direction="column" spacing={1} sx={{ width: '100%' }}>
                    <Stack
                      direction="row"
                      spacing={2}
                      sx={{ width: '100%', alignItems: 'center' }}
                    >
                      <Stack sx={{ width: '100%' }}>
                        <Typography variant="body2" sx={{ fontWeight: 600 }}>
                          Enter Fee (US Dollars)
                          <Box component="span" sx={{ color: 'primary.main' }}>
                            *
                          </Box>
                        </Typography>
                        <TextField
                          hiddenLabel
                          size="small"
                          type="text"
                          placeholder="Enter Handling Fee"
                          sx={{
                            width: '100%',
                            '.MuiInputBase-input.MuiOutlinedInput-input': {
                              py: '12px !important',
                            },
                            background: '#FFFFFF',
                            '& .MuiOutlinedInput-root': {
                              '& fieldset': {
                                border: `1px solid #D0D5DD !important`,
                                borderRadius: '0.5rem !important',
                              },
                            },

                            color: '#667085',
                          }}
                          margin="normal"
                          {...formik.getFieldProps('handlingCommission')}
                          error={
                            formik.touched.handlingCommission &&
                            Boolean(formik.errors.handlingCommission)
                          }
                          helperText={
                            formik.touched.handlingCommission &&
                            formik.errors.handlingCommission
                          }
                        />
                      </Stack>
                      <PerUnitSelector
                        label="Per Unit*"
                        value={formik.values.unit as UnitType | null}
                        onChange={(value) => {
                          formik.setFieldValue('unit', value)
                        }}
                        error={
                          formik.touched.unit && Boolean(formik.errors.unit)
                        }
                        helperText={
                          (formik.touched.unit && formik.errors.unit) || ''
                        }
                      />
                    </Stack>
                  </Stack>

                  <CustomDatePicker
                    label="Select date..."
                    required={true}
                    value={formik.values.effectiveTime || ''}
                    error={
                      !!(
                        formik.touched.effectiveTime &&
                        formik.errors.effectiveTime
                      )
                    }
                    sx={{
                      width: '100%',
                      '.MuiInputBase-input.MuiOutlinedInput-input': {
                        py: '12px !important',
                      },
                      background: '#FFFFFF',
                      '& .MuiOutlinedInput-root': {
                        '& fieldset': {
                          border: `1px solid #D0D5DD !important`,
                          borderRadius: '0.5rem !important',
                        },
                      },

                      color: '#667085',
                    }}
                    onDateChanged={handleDateChange}
                    onBlur={formik.handleBlur}
                    shouldDisableDate={(date) => date.isBefore(dayjs(), 'day')}
                    slotProps={{
                      textField: {
                        error:
                          formik.touched.effectiveTime &&
                          Boolean(formik.errors.effectiveTime),
                        helperText:
                          (formik.touched.effectiveTime &&
                            formik.errors.effectiveTime) ||
                          '',
                      },
                    }}
                  />
                </Stack>
                <Stack direction="row" spacing={2} sx={{ mt: 2 }}>
                  <Button
                    variant="outlined"
                    fullWidth
                    disabled={isConfiguringWarehouseFee}
                    onClick={handleShowDiscardDialog}
                    sx={{
                      mt: 2,
                    }}
                    endIcon={
                      isConfiguringWarehouseFee ? (
                        <CircularProgress size={20} thickness={3.0} />
                      ) : undefined
                    }
                  >
                    {'Cancel'}
                  </Button>

                  <Button
                    variant="contained"
                    fullWidth
                    disabled={isConfiguringWarehouseFee || !formik.isValid}
                    type="submit"
                    sx={{
                      mt: 2,
                      textWrap: 'nowrap',
                    }}
                    endIcon={
                      isConfiguringWarehouseFee ? (
                        <CircularProgress size={20} thickness={3.0} />
                      ) : (
                        <ChevronRight />
                      )
                    }
                  >
                    {'Confirm'}
                  </Button>
                </Stack>
              </Form>
            </FormikProvider>
          </DialogContent>
        </Stack>
      </Dialog>
      <ConfirmFeeConfiguration
        open={showFeeConfirmDialog}
        onClose={handleConfirmDialogClose}
        onConfirm={handleConfirmDialogClose}
        formData={formData}
        warehouseFee={warehouseFee}
      />
      <ConfirmCancel
        open={showDiscardDialog}
        title="Discard Changes"
        description="Are you sure you want to discard your changes?"
        confirmLabel="Discard"
        cancelLabel="Keep Editing"
        onConfirm={handleConfirmDiscard}
        onCancel={() => setShowDiscardDialog(false)}
      />
    </>
  )
}

export default ConfigureWarehouseForm
