'use client'
import { But<PERSON>, Divider, Stack, Typography } from '@mui/material'
import React, { useState } from 'react'
import { ConfigureIcon } from '@dtbx/ui/components/SvgIcons'
import WeeklySalesTable from './weekly-schedule/WeeklySalesTable'
import { CommissionPage } from './commisions/CommissionPage'
import { WarehouseFee } from '@/features/settings/warehouse-fee/WarehouseFee'
import { ScheduleIcon } from '@/components/SvgIcons/ScheduleIcon'
import { WarehouseIcon } from '@/components/SvgIcons/WarehouseIcon'

const SettingToggleButton = ({
  text,
  isActive,
  handleToggle,
  icon,
}: {
  text: string
  isActive: boolean
  handleToggle: () => void
  icon: React.ReactNode
}) => {
  return (
    <Button
      variant="contained"
      color="primary"
      startIcon={icon}
      onClick={handleToggle}
      sx={{
        px: 1.5,
        textWrap: 'nowrap',
        backgroundColor: isActive ? '#00BC2D' : 'white',
        color: isActive ? 'white' : 'black',
        border: isActive ? '1px solid #00BC2D' : '1px solid #ccc',
        '&:hover': {
          backgroundColor: isActive ? '#00A625' : '#f5f5f5',
        },
      }}
    >
      {text}
    </Button>
  )
}

enum ConfigType {
  COMMISSIONS = 'commisions',
  SCHEDULE = 'schedule',
  WAREHOUSE_FEE = 'warehouse_fee',
}

const CONFIG_TOGGLE = [
  {
    text: 'Configure Commissions',
    value: ConfigType.COMMISSIONS,
    icon: (isActive: boolean) => (
      <ConfigureIcon stroke={isActive ? 'white' : 'black'} />
    ),
  },
  {
    text: 'Schedule of Weekly Sales',
    value: ConfigType.SCHEDULE,
    icon: (isActive: boolean) => (
      <ScheduleIcon stroke={isActive ? 'white' : 'black'} />
    ),
  },
  {
    text: 'Warehouse Fees Configuration',
    value: ConfigType.WAREHOUSE_FEE,
    icon: (isActive: boolean) => (
      <WarehouseIcon stroke={isActive ? 'white' : 'black'} />
    ),
  },
]

export default function SettingsPage() {
  const [activeView, setActiveView] = useState<ConfigType>(
    ConfigType.COMMISSIONS
  )

  const handleToggle = (view: ConfigType) => {
    setActiveView(view)
  }

  const isActive = (view: ConfigType) => activeView === view

  return (
    <Stack sx={{ height: '100%' }}>
      <Stack
        paddingInline={3}
        paddingBlock={2}
        sx={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          backgroundColor: '#FFFFFF',
        }}
      >
        <Stack
          sx={{
            flexDirection: 'row',
            alignItems: 'center',
            gap: '8px',
          }}
        >
          <Typography
            variant="h5"
            sx={{
              textAlign: 'left',
              fontWeight: '600',
            }}
          >
            Settings
          </Typography>
        </Stack>
      </Stack>

      <Divider />

      <Stack
        paddingInline={3}
        paddingBlock={2}
        sx={{
          flexDirection: 'row',
          justifyContent: 'space-between',
          alignItems: 'center',
          backgroundColor: '#FFFFFF',
        }}
      >
        <Stack
          sx={{
            flexDirection: 'row',
            alignItems: 'center',
            gap: 2,
          }}
        >
          {CONFIG_TOGGLE.map((config) => (
            <SettingToggleButton
              key={config.value}
              text={config.text}
              isActive={isActive(config.value)}
              handleToggle={() => handleToggle(config.value)}
              icon={config.icon(isActive(config.value))}
            />
          ))}
        </Stack>
      </Stack>

      <Divider />

      <Stack sx={{ height: '100%', overflow: 'hidden' }}>
        {(() => {
          switch (activeView) {
            case ConfigType.COMMISSIONS:
              return <CommissionPage />
            case ConfigType.SCHEDULE:
              return <WeeklySalesTable />
            case ConfigType.WAREHOUSE_FEE:
              return <WarehouseFee />
          }
        })()}
      </Stack>
    </Stack>
  )
}
