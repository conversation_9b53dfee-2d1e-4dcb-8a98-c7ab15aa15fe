import React, { useState } from 'react'
import { Divider, Stack } from '@mui/material'
import { useAppSelector } from '@/store'
import CommissionsTable from './ListCommissions'
import { ConfigureCommissionHeader } from './ConfigureCommissionHeader'
import { CustomTabs, TabType } from '@/components/CustomTabs'
import { TabPanel } from '@dtbx/ui/components/Tabs'

const COMMISSION_TABS: TabType<string>[] = [
  {
    title: 'Current',
    status: 'ACTIVE',
    canSelect: true,
  },
  {
    title: 'Pending Approval',
    status: 'PENDING',
    canSelect: false,
  },
]

export const CommissionPage = () => {
  const [selectedTab, setSelectedTab] = useState(0)
  const handleTabSelected = (index: number) => {
    setSelectedTab(index)
  }

  const { isLoadingCommissions } = useAppSelector(
    (state) => state.brokerCommissions
  )

  return (
    <Stack sx={{ height: '100%', backgroundColor: '#F2F4F7' }}>
      <CustomTabs
        tabs={COMMISSION_TABS}
        selectedTab={selectedTab}
        onTabSelected={handleTabSelected}
        isLoading={isLoadingCommissions}
      />

      <Divider sx={{ margin: '0 auto' }} />

      <ConfigureCommissionHeader />

      <Stack sx={{ overflow: 'auto' }}>
        {COMMISSION_TABS.map((tab, index) => (
          <TabPanel key={tab.title} value={selectedTab} index={index}>
            <CommissionsTable selectedTab={index} />
          </TabPanel>
        ))}
      </Stack>
    </Stack>
  )
}
