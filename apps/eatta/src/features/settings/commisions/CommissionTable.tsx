import {
  BrokerCommissionResponse,
  Order,
  BrokerCommissionStatus,
} from '@/store/interfaces'
import { PaginatedResponse } from '@dtbx/store/interfaces'
import { StatusChip } from '@dtbx/ui/components/Chip'
import {
  CustomPagination,
  CustomTableHeader,
  PaginationOptions,
} from '@dtbx/ui/components/Table'
import { ArrowForwardIos } from '@mui/icons-material'
import {
  Box,
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableFooter,
  TableRow,
  Typography,
} from '@mui/material'
import dayjs from 'dayjs'
import React, { useState } from 'react'
import BrokerCommissionForm from './CommissionDialog'
import { COMMISSION_STATUS_COLOR } from '@/utils/statusChips'
import { useAppDispatch } from '@/store'
import { setSelectedApprovalRequests } from '@/store/reducers/brokerCommissionsReducer'
import {
  ApprovalRequestFilters,
  SelectedApprovalRequests,
} from '@/store/interfaces/makerChecker'
import { IApprovalRequest } from '@/store/interfaces/makerChecker'
import { ApprovalRequestDetails } from './CommissionDialog'

export interface CommissionTableProps {
  filters: ApprovalRequestFilters
  onPageChange: (page: number, size: number) => void
  data: PaginatedResponse<BrokerCommissionResponse | IApprovalRequest>
  isCurrentTab: boolean
}

const CommissionTable: React.FC<CommissionTableProps> = ({
  filters,
  onPageChange,
  data,
  isCurrentTab,
}) => {
  const dispatch = useAppDispatch()
  const [order, setOrder] = useState<Order>('desc')
  const [orderBy, setOrderBy] = useState<string>('dateCreated')
  const [openModal, setOpenModal] = useState<boolean>(false)
  const [selectedRequest, setSelectedRequest] =
    useState<ApprovalRequestDetails | null>(null)

  const handleRequestSort = (
    _event: React.MouseEvent<unknown>,
    property: string
  ) => {
    const isAsc = orderBy === property && order === 'asc'
    const newOrder = isAsc ? 'desc' : 'asc'
    setOrder(newOrder)
    setOrderBy(property as keyof BrokerCommissionResponse)
  }

  const [paginationOptions, setPaginationOptions] = useState({
    page: filters.page,
    size: filters.size,
    totalPages: data.totalNumberOfPages,
  })

  const handlePagination = async (newOptions: PaginationOptions) => {
    setPaginationOptions(newOptions)
    onPageChange(newOptions.page, newOptions.size)
  }

  const handleOpenModal = (row: IApprovalRequest) => {
    const selectedApprovalRequests = JSON.parse(
      row.entity || '{}'
    ) as SelectedApprovalRequests
    dispatch(setSelectedApprovalRequests(selectedApprovalRequests))

    const requestDetails: ApprovalRequestDetails = {
      id: row.id,
      requestType: row.makerCheckerType?.type || '',
      createdAt: row.dateCreated,
      createdBy: row.maker,
      checker: row.checker,
      checkerComments: row.checkerComments,
      maker: row.maker,
      dateCreated: row.dateCreated,
      dateModified: row.dateModified,
      makerCheckerType: row.makerCheckerType,
      entityId: row.entityId || '',
      entity: row.entity || '',
      diff: row.diff,
      makerComments: row.makerComments,
      status: row.status,
    }

    setSelectedRequest(requestDetails)
    setOpenModal(true)
  }

  return (
    <>
      <Box>
        <TableContainer
          component={Paper}
          sx={{
            boxShadow: 'none',
            '& .MuiTableCell-root': {
              paddingInline: '1.5rem',
              paddingBlock: '0.5rem',
              textAlign: 'left',
            },
          }}
        >
          <Table
            sx={{ minWidth: 650 }}
            aria-label="designations table"
            size="small"
          >
            <CustomTableHeader
              order={order}
              orderBy={orderBy}
              headLabel={[
                { id: 'dateSet', label: 'Date Set', alignRight: false },
                {
                  id: 'setBy',
                  label: isCurrentTab ? 'Approved By' : 'Set By',
                  alignRight: false,
                },
                {
                  id: 'brokerBuyerCommission',
                  label: 'Broker Buyer Commission',
                  alignRight: false,
                },
                {
                  id: 'brokerProducerCommission',
                  label: 'Broker Producer Commission',
                  alignRight: false,
                },
                {
                  id: 'CBRRate',
                  label: 'CBR RATE',
                  alignRight: false,
                },
                {
                  id: 'penalty',
                  label: 'Penalty',
                  alignRight: false,
                },
                {
                  id: 'withHoldingTax',
                  label: 'WHT',
                  alignRight: false,
                },
                { id: 'status', label: 'Status', alignRight: false },
                { id: 'action', label: '', alignRight: false },
              ]}
              showCheckbox={false}
              rowCount={2}
              numSelected={2}
              onRequestSort={handleRequestSort}
            />
            <TableBody>
              {data.data.map((row, index) => {
                const isPendingRequest = !isCurrentTab && 'entity' in row
                const selectedApprovalRequest: SelectedApprovalRequests | null =
                  isPendingRequest ? JSON.parse(row.entity || '{}') : null
                const brokerCommission = !isPendingRequest
                  ? (row as BrokerCommissionResponse)
                  : null

                return (
                  <TableRow hover key={index} tabIndex={-1}>
                    <TableCell>
                      <Typography variant="body1">
                        {dayjs(row.dateCreated).format('MMMM D, YYYY')}{' '}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        {dayjs(row.dateCreated).format('h:mm A')}{' '}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body1">
                        {isPendingRequest
                          ? `${selectedApprovalRequest?.createdBy || '<EMAIL>'}`
                          : brokerCommission?.createdBy ||
                            '<EMAIL>'}
                      </Typography>
                      <Typography variant="body2">
                        {isPendingRequest
                          ? `${(row as IApprovalRequest).maker}`
                          : `${brokerCommission?.createByFirstName} ${brokerCommission?.createByLastName}`}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body1">
                        {isPendingRequest
                          ? selectedApprovalRequest?.buyerCommission
                          : brokerCommission?.brokerBuyerCommissionPercentage}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        {isPendingRequest
                          ? selectedApprovalRequest?.commissionType
                          : brokerCommission?.measure}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body1">
                        {isPendingRequest
                          ? selectedApprovalRequest?.producerCommission
                          : brokerCommission?.brokerProducerCommissionPercentage}
                      </Typography>
                      <Typography variant="body2" color="textSecondary">
                        {isPendingRequest
                          ? selectedApprovalRequest?.commissionType
                          : brokerCommission?.measure}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body1">
                        {isPendingRequest
                          ? selectedApprovalRequest?.centralBankRate
                          : brokerCommission?.cbrRate}
                      </Typography>
                      {!isPendingRequest && (
                        <Typography variant="body2" color="textSecondary">
                          {brokerCommission?.measure}
                        </Typography>
                      )}
                    </TableCell>
                    <TableCell>
                      <Typography variant="body1">
                        {isPendingRequest
                          ? selectedApprovalRequest?.penalty
                          : brokerCommission?.buyerPenalty}
                      </Typography>
                      {!isPendingRequest && (
                        <Typography variant="body2" color="textSecondary">
                          {brokerCommission?.measure}
                        </Typography>
                      )}
                    </TableCell>
                    <TableCell>
                      <Typography variant="body1">
                        {new Intl.NumberFormat('en-US', {
                          maximumSignificantDigits: 2,
                        }).format(
                          isPendingRequest
                            ? selectedApprovalRequest?.withHoldingTax || 0
                            : brokerCommission?.withHoldingTax || 0
                        )}
                      </Typography>
                      {!isPendingRequest && (
                        <Typography variant="body2" color="textSecondary">
                          {brokerCommission?.measure}
                        </Typography>
                      )}
                    </TableCell>
                    <TableCell>
                      {isPendingRequest ? (
                        <StatusChip
                          status={
                            COMMISSION_STATUS_COLOR[
                              row?.status as BrokerCommissionStatus
                            ]
                          }
                          label={row?.status}
                        />
                      ) : (
                        <Typography variant="body1" component="div">
                          <StatusChip
                            label={brokerCommission?.status}
                            status={
                              COMMISSION_STATUS_COLOR[
                                brokerCommission?.status as BrokerCommissionStatus
                              ]
                            }
                          />
                        </Typography>
                      )}
                    </TableCell>

                    <TableCell>
                      {isPendingRequest && (
                        <IconButton
                          onClick={() =>
                            handleOpenModal(row as IApprovalRequest)
                          }
                        >
                          <ArrowForwardIos />
                        </IconButton>
                      )}
                    </TableCell>
                  </TableRow>
                )
              })}
            </TableBody>
            <TableFooter>
              <TableRow>
                <TableCell
                  sx={{ paddingInline: 0 }}
                  align="center"
                  height={40}
                  colSpan={12}
                >
                  {data.totalNumberOfPages > 0 && (
                    <CustomPagination
                      options={{
                        ...paginationOptions,
                        totalPages: data.totalNumberOfPages,
                      }}
                      handlePagination={handlePagination}
                    />
                  )}
                </TableCell>
              </TableRow>
            </TableFooter>
          </Table>
        </TableContainer>
      </Box>

      <BrokerCommissionForm
        open={openModal}
        handleClose={() => {
          setOpenModal(false)
          setSelectedRequest(null)
        }}
        actionType="CHECKER"
        requestDetails={selectedRequest || undefined}
      />
    </>
  )
}

export default CommissionTable
