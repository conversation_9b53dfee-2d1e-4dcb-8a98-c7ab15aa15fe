import React, { FC, useState } from 'react'
import {
  Button,
  Typography,
  Stack,
  CircularProgress,
  Link,
} from '@mui/material'
import MakerCheckerActivities from '@/components/MakerCheckerActivities'
import { HelpIcon } from '@dtbx/ui/components/SvgIcons'
import { KeyboardArrowRight } from '@mui/icons-material'
import DeliveryDetails from './DeliveryDetails'
import DeliveryOrderInfo from './DeliveryOrderInfo'
import { LotInfoCardList } from './LotInfoCard'
import { useAppDispatch, useAppSelector } from '@/store'
import { formatDate, formatTimeOnly } from '@dtbx/store/utils'
import { downloadTrdEdo, submitDeliveryOrder } from '@/store/actions/Edo'
import { checkIsMaker } from '@/utils/authUtil'
import { useCustomRouter } from '@dtbx/ui/hooks'

export interface DeliveryOrderSetupProps {
  onNext: () => void
  actionType?: 'MAKER' | 'CHECKER'
}

const DeliveryOrderSetup: FC<DeliveryOrderSetupProps> = ({
  onNext,
  actionType,
}) => {
  const router = useCustomRouter()
  const dispatch = useAppDispatch()
  const isCheckerView = actionType === 'CHECKER'

  const { selectedDeliveryOrderEntries, isSubmittingDeliveryOrder, filters } =
    useAppSelector((state) => state.edo)
  const lotCount = selectedDeliveryOrderEntries?.entries?.length || 4

  const isPendingApproval =
    selectedDeliveryOrderEntries?.status === 'PENDING_APPROVAL'

  const handleDownloadTrd = () => {
    if (selectedDeliveryOrderEntries?.trdDownloadUrl) {
      downloadTrdEdo(
        dispatch,
        selectedDeliveryOrderEntries?.trdDownloadUrl,
        'TRD'
      )
    }
  }

  const handleSubmitDeliveryOrder = () => {
    if (isCheckerView) {
      onNext()
    } else {
      submitDeliveryOrder(
        dispatch,
        selectedDeliveryOrderEntries?.id || '',
        () => {
          const params = new URLSearchParams({
            tab: '1',
            year: filters.year?.toString() || new Date().getFullYear().toString(),
            saleDate: filters.saleDate || '',
          })
          router.replace(`/delivery-orders?${params.toString()}`)
        }
      )
    }
  }

  return (
    <Stack sx={{ width: 'fit-content' }}>
      <Stack direction="row" alignItems="center" spacing={1}>
        <Stack direction="row" spacing={2.5} sx={{ alignItems: 'center' }}>
          <Typography
            sx={{
              fontSize: '1.5rem',
              fontWeight: 600,
              color: '#000A12',
            }}
          >
            {isCheckerView
              ? 'Approve Delivery Order'
              : 'Creating New Delivery Order'}
          </Typography>

          <Typography
            sx={{ fontSize: '1.125rem', fontWeight: 500, color: '#101828' }}
          >
            {selectedDeliveryOrderEntries?.docNumber || ''}
          </Typography>
        </Stack>
        <HelpIcon />
      </Stack>

      {/*Delivery Order Details*/}

      <Stack sx={{ width: '100%' }}>
        <DeliveryOrderInfo />
        <DeliveryDetails />
      </Stack>

      <Typography
        sx={{
          color: '#101828',
          fontWeight: '700',
          textAlign: 'left',
          marginBottom: '1.125rem',
        }}
      >
        Tea Details - {lotCount} {lotCount === 1 ? 'Lot' : 'Lots'}
      </Typography>

      <Typography
        sx={{
          color: '#000A12',
          fontWeight: '400',
          textAlign: 'left',
          marginBottom: '1rem',
          fontSize: '1rem',
        }}
      >
        Below are the teas paid on{' '}
        {formatDate(selectedDeliveryOrderEntries?.dateCreated || '')} for this
        delivery as per the TRD No. {selectedDeliveryOrderEntries?.trdDocNumber}{' '}
        generated on{' '}
        {formatTimeOnly(selectedDeliveryOrderEntries?.dateCreated || '')}.
        &nbsp;
        <Link
          onClick={handleDownloadTrd}
          sx={{ textDecoration: 'underline', cursor: 'pointer' }}
        >
          Download TRD
        </Link>
      </Typography>

      {/*Lot Info*/}
      <Stack sx={{ width: '100%' }}>
        <LotInfoCardList />
      </Stack>

      {/*CTA*/}
      {isCheckerView && (
        <MakerCheckerActivities
          activities={[
            {
              action: 'Delivery order created by',
              email: selectedDeliveryOrderEntries?.modifiedBy || '_',
              actionedBy: selectedDeliveryOrderEntries?.modifiedBy || '_',
              actionedDate: selectedDeliveryOrderEntries?.dateCreated || '_',
            },
          ]}
        />
      )}
      <Button
        fullWidth
        variant="contained"
        disabled={
          isSubmittingDeliveryOrder ||
          checkIsMaker(selectedDeliveryOrderEntries?.modifiedBy) ||
          (isPendingApproval && !isCheckerView)
        }
        onClick={handleSubmitDeliveryOrder}
        sx={{
          mt: 2,
        }}
        endIcon={
          isSubmittingDeliveryOrder ? (
            <CircularProgress size={20} thickness={3.0} />
          ) : (
            <KeyboardArrowRight />
          )
        }
      >
        {isCheckerView
          ? 'Approve and Sign'
          : 'Submit to Checker for Verification'}
      </Button>
    </Stack>
  )
}

export default DeliveryOrderSetup
