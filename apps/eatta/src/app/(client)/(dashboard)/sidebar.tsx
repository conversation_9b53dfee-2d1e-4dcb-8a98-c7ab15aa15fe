import { ISidebarConfigItem } from '@dtbx/ui/components/Sidebar'
import {
  CompaniesIcon,
  HelpIcon,
  PaymentsPageIcon,
  ReceiptIcon,
  StatementsIcon,
} from '@dtbx/ui/icons'
import { ClientType } from '@dtbx/store/interfaces'
import { InvoiceIcon } from '@/components/SvgIcons/InvoiceIcon'
import {
  PostAuctionIcon,
  PreAuctionIcon,
} from '@/components/SvgIcons/AuctionIcon'
import { BankIcon } from '@/components/SvgIcons/BankIcon'

const insightSideBar: ISidebarConfigItem = {
  id: 'insights',
  title: 'Insights',
  path: '/insights',
  module: 'default',
  icon: <PaymentsPageIcon />,
  isProductionReady: true,
}

const supportItem: ISidebarConfigItem = {
  id: 'support',
  title: 'Support',
  path: '/support',
  module: 'default',
  icon: <HelpIcon height="24" width="24" stroke="#667085" />,
  isProductionReady: true,
}

const saleItem: ISidebarConfigItem = {
  id: 'sales',
  title: 'Sales',
  path: '/post-auction',
  module: 'default',
  icon: <PostAuctionIcon />,
  isProductionReady: true,
}

const preSaleItem: ISidebarConfigItem = {
  id: 'pre_sale',
  title: 'Pre-sale',
  path: '/pre-auction',
  module: 'default',
  icon: <PreAuctionIcon />,
  isProductionReady: true,
}

const statementItem: ISidebarConfigItem = {
  id: 'statement',
  title: 'Statement',
  path: '/statement',
  module: 'default',
  icon: <StatementsIcon />,
  isProductionReady: true,
}

const invoicesItem: ISidebarConfigItem = {
  id: 'invoices',
  title: 'Invoices',
  path: '/invoices?tab=0',
  module: 'default',
  icon: <InvoiceIcon />,
  isProductionReady: true,
}

export const producerSideConfig = [
  saleItem,
  {
    id: 'account_sales',
    title: 'Account Sales',
    path: '/account-sales',
    module: 'default',
    icon: <ReceiptIcon />,
    isProductionReady: true,
  },
  statementItem,
  {
    id: 'disbursements',
    title: 'Disbursements',
    path: '/disbursements',
    module: 'default',
    icon: <BankIcon />,
    isProductionReady: true,
  },
  {
    id: 'factories',
    title: 'Factories',
    path: '/factories',
    module: 'default',
    icon: <CompaniesIcon stroke="#667085" />,
    isProductionReady: true,
  },
  supportItem,
]
const buyerSidebarConfig: ISidebarConfigItem[] = [
  preSaleItem,
  saleItem,
  invoicesItem,
  supportItem,
]

const baseSidebarConfig: ISidebarConfigItem[] = [
  preSaleItem,
  saleItem,
  invoicesItem,
  statementItem,
  {
    id: 'delivery_orders',
    title: 'Delivery Orders',
    path: '/delivery-orders',
    module: 'default',
    icon: <PostAuctionIcon />,
    isProductionReady: true,
  },
  supportItem,
]

export const getSidebarConfig = (
  clientType: ClientType | undefined
): ISidebarConfigItem[] => {
  if (clientType === 'Partner') {
    return [insightSideBar, ...baseSidebarConfig.slice(0, 2)]
  }

  if (clientType === 'Warehouse') {
    return baseSidebarConfig.slice(0, 2)
  }

  if (clientType === 'Producer') {
    return producerSideConfig
  }

  if (clientType === 'Buyer') {
    return buyerSidebarConfig
  }

  // For broker
  return baseSidebarConfig
}
