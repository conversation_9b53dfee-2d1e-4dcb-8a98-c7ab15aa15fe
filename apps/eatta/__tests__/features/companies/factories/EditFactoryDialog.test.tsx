import { describe, it, expect, vi, beforeEach } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { Provider } from 'react-redux'
import { configureStore } from '@reduxjs/toolkit'
import { EditFactoryForm } from '../../../../src/features/companies/factories/EditFactoryDialog'
import { companiesReducer } from '../../../../src/store/reducers'

// Mock the MUI Tel Input
vi.mock('mui-tel-input', () => ({
  MuiTelInput: ({ value, onChange, error, helperText, ...props }: any) => (
    <div>
      <input
        data-testid="phone-input"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        {...props}
      />
      {error && helperText && (
        <div data-testid="phone-error">{helperText}</div>
      )}
    </div>
  ),
  matchIsValidTel: vi.fn((tel: string) => {
    // Simple validation for testing - valid if starts with + and has at least 10 digits
    return /^\+\d{10,}$/.test(tel)
  }),
}))

// Mock other dependencies
vi.mock('@dtbx/store', () => ({
  useAppDispatch: () => vi.fn(),
}))

vi.mock('../../../../src/store/actions', () => ({
  fetchBanks: vi.fn(),
  updateCompany: vi.fn(),
  getFactories: vi.fn(),
}))

vi.mock('../../../../src/utils/appTypeChecker', () => ({
  checkIfBackOffice: () => true,
}))

vi.mock('@dtbx/store/utils', () => ({
  HasAccessToRights: () => true,
}))

const mockFactory = {
  id: '1',
  factoryName: 'Test Factory',
  bankName: 'Test Bank',
  bankCode: '001',
  phoneNumber: '+************',
  emailAddress: '<EMAIL>',
  bankBranchName: 'Main Branch',
  bankBranchCode: '001',
  accountNumber: '**********',
  accountCurrency: 'KES',
  swiftCode: 'TESTKE22',
  channel: 'SWIFT' as any,
}

const mockStore = configureStore({
  reducer: {
    companies: companiesReducer,
  },
  preloadedState: {
    companies: {
      banks: [],
      bankBranches: [],
      isEditingUser: false,
      selectedCompany: { id: '1', name: 'Test Company' },
      selectedFactory: mockFactory,
      isLoadingOrganization: false,
      isCreatingFactory: false,
      companiesResponse: { data: [], totalElements: 0, totalNumberOfPages: 0 },
      factoriesResponse: { data: [], totalElements: 0, totalNumberOfPages: 0 },
      warehousesResponse: { data: [], totalElements: 0, totalNumberOfPages: 0 },
      usersResponse: { data: [], totalElements: 0, totalNumberOfPages: 0 },
      isLoadingUsers: false,
      isCreatingUser: false,
      selectedUser: null,
      isLoadingFactories: false,
      isLoadingWarehouses: false,
      isCreatingWarehouse: false,
      selectedWarehouse: null,
    },
  },
})

const renderWithProvider = (component: React.ReactElement) => {
  return render(
    <Provider store={mockStore}>
      {component}
    </Provider>
  )
}

describe('EditFactoryForm Phone Number Validation', () => {
  const mockOnEditSuccess = vi.fn()
  const mockOnCancel = vi.fn()

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should render phone number field without required asterisk', () => {
    renderWithProvider(
      <EditFactoryForm
        onEditSuccess={mockOnEditSuccess}
        onCancel={mockOnCancel}
        company={null}
        factory={mockFactory}
      />
    )

    // Check that phone number label doesn't have required asterisk
    const phoneLabel = screen.getByText('Phone Number')
    expect(phoneLabel).toBeInTheDocument()
    
    // Check that the phone input is rendered
    const phoneInput = screen.getByTestId('phone-input')
    expect(phoneInput).toBeInTheDocument()
    expect(phoneInput).toHaveValue('+************')
  })

  it('should show validation error for invalid phone number', async () => {
    renderWithProvider(
      <EditFactoryForm
        onEditSuccess={mockOnEditSuccess}
        onCancel={mockOnCancel}
        company={null}
        factory={mockFactory}
      />
    )

    const phoneInput = screen.getByTestId('phone-input')
    
    // Enter invalid phone number
    fireEvent.change(phoneInput, { target: { value: 'invalid-phone' } })

    // Wait for validation to trigger
    await waitFor(() => {
      const errorMessage = screen.queryByTestId('phone-error')
      if (errorMessage) {
        expect(errorMessage).toHaveTextContent('Invalid phone number')
      }
    })
  })

  it('should not show validation error for valid phone number', async () => {
    renderWithProvider(
      <EditFactoryForm
        onEditSuccess={mockOnEditSuccess}
        onCancel={mockOnCancel}
        company={null}
        factory={mockFactory}
      />
    )

    const phoneInput = screen.getByTestId('phone-input')
    
    // Enter valid phone number
    fireEvent.change(phoneInput, { target: { value: '+254712345679' } })

    // Wait a bit to ensure validation has run
    await waitFor(() => {
      const errorMessage = screen.queryByTestId('phone-error')
      expect(errorMessage).not.toBeInTheDocument()
    })
  })

  it('should allow empty phone number (optional field)', async () => {
    renderWithProvider(
      <EditFactoryForm
        onEditSuccess={mockOnEditSuccess}
        onCancel={mockOnCancel}
        company={null}
        factory={mockFactory}
      />
    )

    const phoneInput = screen.getByTestId('phone-input')
    
    // Clear phone number
    fireEvent.change(phoneInput, { target: { value: '' } })

    // Wait a bit to ensure validation has run
    await waitFor(() => {
      const errorMessage = screen.queryByTestId('phone-error')
      expect(errorMessage).not.toBeInTheDocument()
    })
  })
})
