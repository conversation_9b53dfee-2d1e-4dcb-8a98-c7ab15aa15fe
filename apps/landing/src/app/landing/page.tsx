'use client'
import { Card, Divider, InputAdornment, Stack, Typography } from '@mui/material'
import React, { useEffect, useState } from 'react'
import SearchOutlinedIcon from '@mui/icons-material/SearchOutlined'
import {
  CardsIcon,
  LandingVector,
  LogoWhite,
  NotificationIcon,
  StaffUsersIcon,
  X247Icon,
} from '@dtbx/ui/icons'
import { CustomSearchInput } from '@dtbx/ui/components/Input'
import { getTimeOfDay } from '@dtbx/store/utils'
import { ILandingApps } from '@dtbx/store/interfaces'
import { useAppDispatch, useAppSelector } from '@dtbx/store'
import { fetchChannelModules } from '@dtbx/store/actions'
import { Payments, EnergySavingsLeaf } from '@mui/icons-material'

const isUat = process.env.NEXT_PUBLIC_ENVIRONMENT === 'uat'
const isProd = process.env.NEXT_PUBLIC_ENVIRONMENT === 'prod'
const isDev = process.env.NEXT_PUBLIC_ENVIRONMENT === 'dev'

const apps: ILandingApps[] = [
  {
    name: 'x24/7 Back Office',
    key: 'x247',
    channel: 'DBP',
    icon: <X247Icon />,
    url: '/dashboard/home',
    isProductionReady: true,
  },
  {
    name: 'Loan Management System',
    key: 'lms',
    channel: 'LMS',
    url: '/lms/home',
    isProductionReady: true,
  },
  {
    name: 'Settlement Engine',
    key: 'eatta',
    channel: 'EATTA',
    url: '/eatta/backoffice/pre-auction',
    isProductionReady: !isProd,
  },
  {
    name: 'Access Management',
    key: 'iam',
    channel: 'IAM',
    icon: <StaffUsersIcon stroke="#EB0045" />,
    url: 'ams/users',
    isProductionReady: true,
  },
  {
    name: 'Notifications Management',
    key: 'notifications',
    channel: 'DBP',
    icon: <NotificationIcon stroke={'#EB0045'} />,
    url: 'nms/home',
    isProductionReady: !isProd,
  },
  {
    name: 'Card Management',
    key: 'cards',
    channel: 'DBP',
    icon: <CardsIcon stroke={'#EB0045'} />,
    url: 'cms/credit-cards',
    isProductionReady: true,
  },
  {
    name: 'Money Transfer Services',
    key: 'MTS',
    channel: 'MTO',
    url: 'mts/organizations',
    icon: <Payments sx={{ color: '#EB0045' }} />,
    isProductionReady: !isProd,
  },
  {
    name: 'Boma Yangu',
    key: 'BMS',
    channel: 'BOMA',
    url: '/boma-yangu/home',
    icon: <EnergySavingsLeaf sx={{ color: '#EB0045' }} />,
    isProductionReady: isDev,
  },
]
export default function Landing() {
  const dispatch = useAppDispatch()
  const { timeOfDay, formattedTime } = getTimeOfDay()
  const profile = useAppSelector((state) => state.auth.decodedToken)
  const modules = useAppSelector((state) => state.auth.channelModules) || []
  const [searchValue, setSearchValue] = useState<string>('')

  useEffect(() => {
    // Fetch channel modules on component mount
    fetchChannelModules(dispatch)
  }, [dispatch])

  // Filter apps based on channel availability and production readiness
  const filteredApps = apps.filter((app) => {
    const channelExists = modules.some(
      (module) => module.channel === app.channel
    )
    // Always show Boma Yangu in dev, even if channel is missing
    // if (app.key === 'BMS' && isDev) {
    //   return app.isProductionReady
    // }
    return channelExists && app.isProductionReady
  })

  // Handle search input changes
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchValue(e.target.value)
  }

  return (
    <Stack
      sx={{
        flexDirection: 'column',
        px: '5%',
        py: '2%',
        gap: '20px',
        width: '100%',
      }}
    >
      <Typography variant="h5">
        Good {timeOfDay}, {profile.first_name}.
      </Typography>
      <Typography variant="subtitle2">{formattedTime}</Typography>
      <Stack
        sx={{
          width: '100%',
          backgroundImage: 'url("/landingBgPattern.svg")',
          justifyContent: 'space-between',
          flexDirection: 'row',
          paddingLeft: '2%',
          paddingBottom: '2%',
        }}
      >
        <LogoWhite />
        <Stack
          sx={{
            gap: '20px',
            flexDirection: 'column',
            justifyContent: 'center',
          }}
        >
          <Typography
            variant="h6"
            sx={{
              color: '#FFF',
              fontWeight: 800,
              textTransform: 'uppercase',
            }}
          >
            The impossible we do immediately,
            <br /> miracles take a little longer.
          </Typography>
          <Divider color={'#FFF'} />
          <Typography
            sx={{
              color: '#FFF',
              fontSize: '19px',
              fontWeight: 540,
              lineHeight: '24px',
            }}
          >
            It always seems impossible until it’s done.
          </Typography>
        </Stack>
        <Stack sx={{ justifyContent: 'flex-end', alignItems: 'flex-end' }}>
          <LandingVector />
        </Stack>
      </Stack>
      <Typography variant="subtitle1">DTBx Services</Typography>
      <CustomSearchInput
        value={searchValue}
        onChange={handleSearch}
        placeholder="Search"
        startAdornment={
          <InputAdornment position="start">
            <SearchOutlinedIcon sx={{ color: 'text.disabled' }} />
          </InputAdornment>
        }
      />
      <Stack sx={{ flexDirection: 'row', gap: '2rem', flexWrap: 'wrap' }}>
        {filteredApps.map((app) => (
          <Card
            key={app.key}
            sx={{
              px: '2rem',
              py: '3rem',
              width: '12rem',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              gap: '10px',
              cursor: 'pointer',
              boxShadow: '0px 1.381px 8.288px 0px rgba(0, 0, 0, 0.05)',
              border: '1px solid #F7F7F7',
              borderRadius: '14px',
              '&:hover': {
                border: '1px solid #EB0045',
              },
              textDecoration: 'none',
            }}
            component={'a'}
            href={app.url}
          >
            {app.icon ? (
              <Stack
                sx={{
                  borderRadius: '3px',
                  border: '1px solid #E3E4E4',
                  width: '60px',
                  height: '60px',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                {app.icon}
              </Stack>
            ) : (
              <Stack
                sx={{
                  borderRadius: '3px',
                  background: '#FFE1EA',
                  width: '60px',
                  height: '60px',
                  alignItems: 'center',
                  justifyContent: 'center',
                }}
              >
                <Typography variant="body1" sx={{ color: 'secondary.main' }}>
                  {app.key.toUpperCase()}
                </Typography>
              </Stack>
            )}
            <Typography
              variant="body2"
              sx={{ color: '#2A3339', textAlign: 'center' }}
            >
              {app.name}
            </Typography>
          </Card>
        ))}
      </Stack>
    </Stack>
  )
}
