import type { NextConfig } from 'next'

const {
  X247_URL,
  LMS_URL,
  EATTA_URL,
  AMS_URL,
  NMS_URL,
  CMS_URL,
  BOMA_YANGU_URL,
  MTS_URL,
} = process.env

const nextConfig: NextConfig = {
  output: 'standalone',
  async rewrites() {
    return [
      /**
       * Rewrites for Multi Zones
       */
      {
        source: '/dashboard',
        destination: `${X247_URL}/dashboard`,
      },
      {
        source: '/dashboard/:path*',
        destination: `${X247_URL}/dashboard/:path*`,
      },
      {
        source: '/lms',
        destination: `${LMS_URL}/lms`,
      },
      {
        source: '/lms/:path*',
        destination: `${LMS_URL}/lms/:path*`,
      },
      {
        source: '/eatta',
        destination: `${EATTA_URL}/eatta`,
      },
      {
        source: '/eatta/:path*',
        destination: `${EATTA_URL}/eatta/:path*`,
      },
      {
        source: '/ams',
        destination: `${AMS_URL}/ams`,
      },
      {
        source: '/ams/:path*',
        destination: `${AMS_URL}/ams/:path*`,
      },
      {
        source: '/nms',
        destination: `${NMS_URL}/nms`,
      },
      {
        source: '/nms/:path*',
        destination: `${NMS_URL}/nms/:path*`,
      },
      {
        source: '/cms',
        destination: `${CMS_URL}/cms`,
      },
      {
        source: '/cms/:path*',
        destination: `${CMS_URL}/cms/:path*`,
      },
      {
        source: '/mts',
        destination: `${MTS_URL}/mts`,
      },
      {
        source: '/mts/:path*',
        destination: `${MTS_URL}/mts/:path*`,
      },
      {
        source: '/boma-yangu',
        destination: `${BOMA_YANGU_URL}/boma-yangu`,
      },
      {
        source: '/boma-yangu/:path*',
        destination: `${BOMA_YANGU_URL}/boma-yangu/:path*`,
      },
    ]
  },
  experimental: {
    swcPlugins:
      process.env.NEXT_PUBLIC_TEST === 'e2e'
        ? [
            [
              'swc-plugin-coverage-instrument',
              {
                unstableExclude: ['**/node_modules/**'],
              },
            ],
          ]
        : [],
  },
}

export default nextConfig
